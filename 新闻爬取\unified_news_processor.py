"""
统一新闻处理器 - 改进版
支持统一向量数据库、自动文件发现、重复检测、文件清理等功能
"""

import pandas as pd
import requests
import json
import time
import re
import sys
import os
import glob
import shutil
import hashlib
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import logging

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from vector_database import VectorDatabase
    from config import get_embedding_config, get_system_config
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("💡 请确保相关文件在当前目录")
    sys.exit(1)


class UnifiedNewsProcessor:
    """统一新闻处理器 - 支持持久化向量数据库和智能文件管理"""
    
    def __init__(self, config=None):
        """
        初始化统一新闻处理器
        
        Args:
            config: 系统配置，如果为None则使用默认配置
        """
        # 加载配置
        self.embedding_config = get_embedding_config()
        self.system_config = get_system_config() if config is None else config
        
        # 确保目录存在
        self.system_config.ensure_directories()
        
        # 初始化统一向量数据库
        print(f"🗄️ 初始化统一向量数据库: {self.system_config.VECTOR_DB_PATH}")
        self.vector_db = VectorDatabase(
            db_path=self.system_config.VECTOR_DB_PATH,
            vector_dim=self.system_config.VECTOR_DIMENSION
        )
        
        # 初始化HTTP会话
        self.session = requests.Session()
        
        # 统计信息
        self.stats = {
            'total_processed': 0,
            'successful_vectorized': 0,
            'failed_vectorized': 0,
            'skipped_duplicates': 0,
            'skipped_low_quality': 0,
            'files_processed': 0,
            'files_archived': 0,
            'files_deleted': 0
        }
        
        # 线程安全锁
        self.stats_lock = threading.Lock()
        self.request_lock = threading.Lock()
        
        # 设置日志
        self._setup_logging()
        
        print(f"✅ 统一新闻处理器初始化完成")
        print(f"   向量数据库: {self.system_config.VECTOR_DB_PATH}")
        print(f"   CSV目录: {self.system_config.CSV_DIR}")
        print(f"   归档目录: {self.system_config.ARCHIVE_DIR}")
    
    def _setup_logging(self):
        """设置日志"""
        log_file = self.system_config.get_log_file_path("unified_processor")
        
        logging.basicConfig(
            level=getattr(logging, self.system_config.LOG_LEVEL),
            format=self.system_config.LOG_FORMAT,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def discover_csv_files(self) -> List[str]:
        """发现待处理的CSV文件"""
        pattern = self.system_config.get_csv_pattern()
        csv_files = glob.glob(pattern)
        
        # 过滤掉已处理的文件（可以通过文件名或修改时间判断）
        unprocessed_files = []
        
        for csv_file in csv_files:
            # 检查文件是否太旧
            file_time = datetime.fromtimestamp(os.path.getctime(csv_file))
            age_days = (datetime.now() - file_time).days
            
            if age_days <= self.system_config.MAX_CSV_AGE_DAYS:
                unprocessed_files.append(csv_file)
            else:
                self.logger.info(f"跳过过旧文件: {csv_file} (创建于 {age_days} 天前)")
        
        # 按修改时间排序，优先处理新文件
        unprocessed_files.sort(key=os.path.getmtime, reverse=True)
        
        self.logger.info(f"发现 {len(unprocessed_files)} 个待处理CSV文件")
        return unprocessed_files
    
    def load_csv_data(self, csv_file_path: str) -> pd.DataFrame:
        """加载CSV新闻数据"""
        self.logger.info(f"📂 加载CSV文件: {csv_file_path}")
        
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_file_path, encoding='utf-8')
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条新闻记录")
            self.logger.info(f"📊 数据列: {list(df.columns)}")
            
            # 显示数据概览
            if not df.empty:
                self.logger.info(f"📋 数据概览:")
                self.logger.info(f"   - 标题示例: {df['title'].iloc[0][:50]}...")
                if 'media' in df.columns:
                    self.logger.info(f"   - 媒体来源: {df['media'].value_counts().head(3).to_dict()}")
                if 'category' in df.columns:
                    self.logger.info(f"   - 分类分布: {df['category'].value_counts().head(3).to_dict()}")
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载CSV文件失败: {e}")
            return pd.DataFrame()
    
    def clean_content(self, content: str) -> str:
        """清洗新闻内容"""
        if pd.isna(content) or not content:
            return ""
        
        # 转换为字符串
        content = str(content)
        
        # 移除多余的换行和空格
        content = re.sub(r'\n+', '\n', content)
        content = re.sub(r'\s+', ' ', content)
        
        # 移除特殊字符
        content = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,!?;:()（）"""]', '', content)
        
        # 移除过短的句子
        sentences = content.split('。')
        valid_sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        
        return '。'.join(valid_sentences).strip()
    
    def evaluate_content_quality(self, title: str, content: str) -> float:
        """评估内容质量"""
        score = 0.0
        
        # 标题质量 (0-0.3)
        if title and len(title) > 5:
            score += 0.1
        if title and len(title) > 15:
            score += 0.1
        if title and not any(char in title for char in ['？', '！', '…']):
            score += 0.1
        
        # 内容长度 (0-0.4)
        if len(content) > 100:
            score += 0.1
        if len(content) > 300:
            score += 0.1
        if len(content) > 800:
            score += 0.1
        if len(content) > 1500:
            score += 0.1
        
        # 内容结构 (0-0.3)
        if '。' in content:
            score += 0.1
        if content.count('。') >= 3:
            score += 0.1
        if any(keyword in content for keyword in ['报道', '消息', '据悉', '记者', '新华社', '中新网']):
            score += 0.1
        
        return min(score, 1.0)
    
    def _rate_limit_wait(self):
        """API限流控制"""
        current_time = time.time()
        if hasattr(self, 'last_request_time'):
            time_since_last = current_time - self.last_request_time
            min_interval = 60 / self.embedding_config.get('rate_limit', 60)
            
            if time_since_last < min_interval:
                wait_time = min_interval - time_since_last
                time.sleep(wait_time)
        
        self.last_request_time = time.time()
    
    def get_embeddings(self, texts: List[str], thread_id: str = "") -> Optional[List[List[float]]]:
        """获取文本向量（线程安全）"""
        if len(texts) > self.embedding_config.get('batch_size', 10):
            raise ValueError(f"API最多支持{self.embedding_config.get('batch_size', 10)}个文本")

        # 使用锁确保API调用的线程安全
        with self.request_lock:
            self._rate_limit_wait()

            url = self.embedding_config['base_url'].format(
                project_id=self.embedding_config['project_id']
            )

            headers = {
                'Content-Type': 'application/json',
                'Authorization': f"Bearer {self.embedding_config['api_key']}"
            }

            # 检查文本内容
            for i, text in enumerate(texts):
                max_tokens = self.embedding_config.get('max_tokens', 8000)
                if len(text) > max_tokens:
                    texts[i] = text[:max_tokens] + "..."
                    self.logger.warning(f"[{thread_id}] 文本 {i+1} 过长，已截断到{max_tokens}字符")

            data = {
                "easyllm_id": self.embedding_config['easyllm_id'],
                "input_texts": texts,
                "dimensions": self.embedding_config['dimensions']
            }

            self.logger.info(f"📊 [{thread_id}] 发送 {len(texts)} 个文本进行向量化")

            try:
                timeout = self.embedding_config.get('timeout', 30)
                response = self.session.post(url, headers=headers, json=data, timeout=timeout)
                response.raise_for_status()

                result = response.json()
                embeddings = [item['embedding'] for item in result['data']]

                self.logger.info(f"✅ [{thread_id}] API调用成功，获得 {len(embeddings)} 个向量")
                return embeddings

            except requests.exceptions.HTTPError as e:
                self.logger.error(f"❌ [{thread_id}] HTTP错误: {e}")
                self.logger.error(f"📄 响应状态码: {response.status_code}")
                self.logger.error(f"📄 响应内容: {response.text}")
                return None
            except Exception as e:
                self.logger.error(f"❌ [{thread_id}] API请求失败: {e}")
                return None

    def process_news_batch(self, news_batch: List[Dict], batch_num: int, total_batches: int, thread_id: str = ""):
        """处理一批新闻（支持多线程）"""
        self.logger.info(f"🔄 [{thread_id}] 处理第 {batch_num}/{total_batches} 批次 ({len(news_batch)} 条新闻)...")

        # 准备向量化的文本
        texts_for_embedding = []
        valid_news = []

        for news in news_batch:
            # 清洗内容
            cleaned_content = self.clean_content(news.get('content', ''))

            # 质量评估
            quality_score = self.evaluate_content_quality(news.get('title', ''), cleaned_content)

            # 质量过滤
            if (len(cleaned_content) >= self.system_config.MIN_CONTENT_LENGTH and
                quality_score >= self.system_config.MIN_QUALITY_SCORE):

                # 准备向量化文本（标题+内容）
                text_for_embedding = f"{news.get('title', '')}\n{cleaned_content}"
                texts_for_embedding.append(text_for_embedding)

                # 保存处理后的新闻信息
                news['cleaned_content'] = cleaned_content
                news['quality_score'] = quality_score
                valid_news.append(news)
            else:
                self.logger.warning(f"⚠️ [{thread_id}] 跳过低质量新闻: {news.get('title', '')[:30]}... "
                                  f"(内容长度: {len(cleaned_content)}, 质量分: {quality_score:.2f})")
                with self.stats_lock:
                    self.stats['skipped_low_quality'] += 1

        if not valid_news:
            self.logger.warning(f"❌ [{thread_id}] 本批次没有符合质量要求的新闻")
            return

        self.logger.info(f"📝 [{thread_id}] 本批次有效新闻: {len(valid_news)} 条")

        # 批量向量化
        embeddings = self.get_embeddings(texts_for_embedding, thread_id)

        if not embeddings:
            self.logger.error(f"❌ [{thread_id}] 向量化失败")
            with self.stats_lock:
                self.stats['failed_vectorized'] += len(valid_news)
            return

        # 存储到向量数据库
        for news, embedding in zip(valid_news, embeddings):
            try:
                # 生成向量ID
                timestamp = int(time.time())
                vector_id = f"{news.get('media', 'unknown')}_{news.get('id', timestamp)}_{timestamp}"

                # 准备元数据
                metadata = {
                    'news_id': news.get('id', ''),
                    'title': news.get('title', ''),
                    'content': news['cleaned_content'],
                    'media': news.get('media', ''),
                    'category': news.get('category', ''),
                    'url': news.get('url', ''),
                    'publish_time': f"{news.get('create_date', '')} {news.get('create_time', '')}".strip(),
                    'quality_score': news['quality_score'],
                    'rank': news.get('rank', 0),
                    'top_num': news.get('top_num', ''),
                    'crawl_time': news.get('crawl_time', ''),
                    'processed_time': datetime.now().isoformat(),
                    'source': 'csv_import'
                }

                # 存储到向量数据库（启用重复检测）
                success, message = self.vector_db.add_document(
                    vector_id,
                    embedding,
                    metadata,
                    check_duplicate=self.system_config.DUPLICATE_CHECK_ENABLED
                )

                if success:
                    with self.stats_lock:
                        self.stats['successful_vectorized'] += 1
                else:
                    if '重复' in message:
                        with self.stats_lock:
                            self.stats['skipped_duplicates'] += 1
                        self.logger.info(f"⚠️ [{thread_id}] {message}")
                    else:
                        with self.stats_lock:
                            self.stats['failed_vectorized'] += 1
                        self.logger.error(f"❌ [{thread_id}] 存储失败: {message}")

            except Exception as e:
                self.logger.error(f"❌ [{thread_id}] 存储新闻失败: {e}")
                with self.stats_lock:
                    self.stats['failed_vectorized'] += 1

        self.logger.info(f"✅ [{thread_id}] 第 {batch_num} 批次处理完成")

    def process_csv_file(self, csv_file_path: str) -> bool:
        """处理单个CSV文件"""
        self.logger.info(f"🚀 开始处理CSV文件: {csv_file_path}")

        # 加载数据
        df = self.load_csv_data(csv_file_path)
        if df.empty:
            self.logger.error(f"❌ CSV文件为空或加载失败: {csv_file_path}")
            return False

        # 转换为字典列表
        news_data = df.to_dict('records')

        # 批量处理
        batch_size = self.system_config.BATCH_SIZE
        max_workers = self.system_config.MAX_WORKERS

        # 分批处理
        batches = [news_data[i:i + batch_size] for i in range(0, len(news_data), batch_size)]
        total_batches = len(batches)

        self.logger.info(f"📊 处理配置:")
        self.logger.info(f"   总记录数: {len(news_data)}")
        self.logger.info(f"   批次大小: {batch_size}")
        self.logger.info(f"   总批次数: {total_batches}")
        self.logger.info(f"   线程数: {max_workers}")

        # 多线程处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            for i, batch in enumerate(batches, 1):
                thread_id = f"T{i:03d}"
                future = executor.submit(self.process_news_batch, batch, i, total_batches, thread_id)
                futures.append(future)

            # 等待所有任务完成
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    self.logger.error(f"❌ 批次处理异常: {e}")

        # 更新文件处理统计
        with self.stats_lock:
            self.stats['files_processed'] += 1
            self.stats['total_processed'] += len(news_data)

        self.logger.info(f"✅ CSV文件处理完成: {csv_file_path}")
        return True

    def manage_csv_file(self, csv_file_path: str) -> bool:
        """管理CSV文件（归档或删除）"""
        try:
            if self.system_config.ARCHIVE_PROCESSED_FILES:
                # 归档文件
                filename = os.path.basename(csv_file_path)
                archive_path = os.path.join(self.system_config.ARCHIVE_DIR, filename)

                # 如果归档文件已存在，添加时间戳
                if os.path.exists(archive_path):
                    name, ext = os.path.splitext(filename)
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    archive_path = os.path.join(self.system_config.ARCHIVE_DIR, f"{name}_{timestamp}{ext}")

                shutil.move(csv_file_path, archive_path)
                self.logger.info(f"📦 文件已归档: {csv_file_path} -> {archive_path}")

                with self.stats_lock:
                    self.stats['files_archived'] += 1

            elif self.system_config.AUTO_CLEANUP_CSV and not self.system_config.KEEP_ORIGINAL_CSV:
                # 删除文件
                os.remove(csv_file_path)
                self.logger.info(f"🗑️ 文件已删除: {csv_file_path}")

                with self.stats_lock:
                    self.stats['files_deleted'] += 1

            return True

        except Exception as e:
            self.logger.error(f"❌ 文件管理失败: {csv_file_path} - {e}")
            return False

    def process_all_csv_files(self) -> Dict:
        """处理所有发现的CSV文件"""
        self.logger.info("🚀 开始统一新闻处理流程")

        # 发现CSV文件
        csv_files = self.discover_csv_files()

        if not csv_files:
            self.logger.info("📭 没有发现待处理的CSV文件")
            return self.get_processing_stats()

        self.logger.info(f"📋 发现 {len(csv_files)} 个CSV文件待处理")

        # 处理每个文件
        for i, csv_file in enumerate(csv_files, 1):
            self.logger.info(f"\n📄 处理文件 {i}/{len(csv_files)}: {os.path.basename(csv_file)}")

            # 处理CSV文件
            success = self.process_csv_file(csv_file)

            if success:
                # 管理文件（归档或删除）
                self.manage_csv_file(csv_file)
            else:
                self.logger.error(f"❌ 文件处理失败，保留原文件: {csv_file}")

        # 保存向量数据库
        self.logger.info("💾 保存向量数据库...")
        self.vector_db.save_database()

        # 显示最终统计
        final_stats = self.get_processing_stats()
        self.print_processing_summary(final_stats)

        return final_stats

    def get_processing_stats(self) -> Dict:
        """获取处理统计信息"""
        with self.stats_lock:
            stats = self.stats.copy()

        # 添加向量数据库统计
        db_stats = self.vector_db.get_stats()
        stats.update({
            'vector_db_stats': {
                'total_documents': db_stats.get('total_documents', 0),
                'total_searches': db_stats.get('total_searches', 0),
                'duplicate_stats': db_stats.get('duplicate_stats', {}),
                'categories': db_stats.get('categories', {}),
                'sources': db_stats.get('sources', {}),
                'avg_quality_score': db_stats.get('avg_quality_score', 0.0)
            }
        })

        return stats

    def print_processing_summary(self, stats: Dict):
        """打印处理摘要"""
        self.logger.info("\n" + "="*60)
        self.logger.info("📊 统一新闻处理摘要")
        self.logger.info("="*60)

        # 文件处理统计
        self.logger.info(f"📁 文件处理:")
        self.logger.info(f"   处理文件数: {stats['files_processed']}")
        self.logger.info(f"   归档文件数: {stats['files_archived']}")
        self.logger.info(f"   删除文件数: {stats['files_deleted']}")

        # 数据处理统计
        self.logger.info(f"\n📊 数据处理:")
        self.logger.info(f"   总处理记录: {stats['total_processed']}")
        self.logger.info(f"   成功向量化: {stats['successful_vectorized']}")
        self.logger.info(f"   跳过重复: {stats['skipped_duplicates']}")
        self.logger.info(f"   跳过低质量: {stats['skipped_low_quality']}")
        self.logger.info(f"   处理失败: {stats['failed_vectorized']}")

        # 向量数据库统计
        db_stats = stats.get('vector_db_stats', {})
        self.logger.info(f"\n🗄️ 向量数据库:")
        self.logger.info(f"   总文档数: {db_stats.get('total_documents', 0)}")
        self.logger.info(f"   平均质量分: {db_stats.get('avg_quality_score', 0):.2f}")

        # 重复检测统计
        dup_stats = db_stats.get('duplicate_stats', {})
        self.logger.info(f"   重复URL: {dup_stats.get('duplicate_urls', 0)}")
        self.logger.info(f"   重复内容: {dup_stats.get('duplicate_contents', 0)}")

        # 分类统计
        categories = db_stats.get('categories', {})
        if categories:
            self.logger.info(f"\n📂 分类分布:")
            for category, count in list(categories.items())[:5]:
                self.logger.info(f"   {category}: {count}")

        self.logger.info("="*60)

    def test_similarity_search(self, query: str, top_k: int = 5):
        """测试相似度搜索"""
        self.logger.info(f"\n🔍 测试相似度搜索: {query}")
        self.logger.info("-" * 40)

        # 获取查询向量
        query_embedding = self.get_embeddings([query])

        if not query_embedding:
            self.logger.error("❌ 获取查询向量失败")
            return

        # 搜索相似新闻
        results = self.vector_db.search(
            query_embedding[0],
            top_k,
            threshold=self.system_config.SIMILARITY_THRESHOLD
        )

        if results:
            self.logger.info(f"✅ 找到 {len(results)} 条相关新闻:")
            for result in results:
                metadata = result['metadata']
                self.logger.info(f"\n{result['rank']}. 相似度: {result['similarity']:.3f}")
                self.logger.info(f"   📰 {metadata.get('title', 'N/A')}")
                self.logger.info(f"   🏷️  {metadata.get('category', 'N/A')} | 📰 {metadata.get('media', 'N/A')}")
                self.logger.info(f"   📅 {metadata.get('publish_time', 'N/A')}")
        else:
            self.logger.info("❌ 未找到相关新闻")


def main():
    """主函数 - 统一新闻处理流程"""
    print("🚀 启动统一新闻处理器")
    print("=" * 60)

    try:
        # 创建处理器
        processor = UnifiedNewsProcessor()

        # 处理所有CSV文件
        stats = processor.process_all_csv_files()

        # 显示向量数据库状态
        print("\n🗄️ 向量数据库状态:")
        processor.vector_db.print_stats()

        # 测试搜索功能
        test_queries = [
            "经济发展",
            "科技创新",
            "疫情防控"
        ]

        for query in test_queries:
            processor.test_similarity_search(query, top_k=3)

        print("\n✅ 统一新闻处理完成!")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断处理")
    except Exception as e:
        print(f"\n❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
