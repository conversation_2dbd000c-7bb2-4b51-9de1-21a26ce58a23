#!/usr/bin/env python3
"""
新闻搜索功能测试脚本
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from news_search import NewsSearchEngine
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def test_basic_search():
    """测试基本搜索功能"""
    print("🧪 测试基本搜索功能")
    print("=" * 50)
    
    try:
        engine = NewsSearchEngine()
        
        # 测试查询列表
        test_queries = [
            "人工智能",
            "新能源汽车", 
            "量子计算",
            "印度军事",
            "经济政策"
        ]
        
        for query in test_queries:
            print(f"\n🔍 测试查询: {query}")
            results = engine.search(query, top_k=3, threshold=0.3)
            
            if results:
                print(f"   ✅ 找到 {len(results)} 条结果")
                best_match = results[0]
                print(f"   📰 最佳匹配: {best_match['metadata'].get('title', '')[:50]}...")
                print(f"   🎯 相似度: {best_match['similarity']:.3f}")
            else:
                print(f"   ❌ 未找到结果")
        
        print(f"\n✅ 基本搜索测试完成")
        
    except Exception as e:
        print(f"❌ 基本搜索测试失败: {e}")


def test_advanced_search():
    """测试高级搜索功能"""
    print("\n🧪 测试高级搜索功能")
    print("=" * 50)
    
    try:
        engine = NewsSearchEngine()
        
        # 测试不同阈值
        query = "人工智能"
        thresholds = [0.3, 0.5, 0.7]
        
        for threshold in thresholds:
            print(f"\n🎯 测试阈值 {threshold}: {query}")
            results = engine.search(query, top_k=5, threshold=threshold)
            print(f"   结果数量: {len(results)}")
            
            if results:
                similarities = [r['similarity'] for r in results]
                print(f"   相似度范围: {min(similarities):.3f} - {max(similarities):.3f}")
        
        print(f"\n✅ 高级搜索测试完成")
        
    except Exception as e:
        print(f"❌ 高级搜索测试失败: {e}")


def test_database_stats():
    """测试数据库统计功能"""
    print("\n🧪 测试数据库统计功能")
    print("=" * 50)
    
    try:
        engine = NewsSearchEngine()
        engine.get_database_stats()
        print(f"\n✅ 数据库统计测试完成")
        
    except Exception as e:
        print(f"❌ 数据库统计测试失败: {e}")


def test_batch_search():
    """测试批量搜索功能"""
    print("\n🧪 测试批量搜索功能")
    print("=" * 50)
    
    try:
        engine = NewsSearchEngine()
        
        # 创建临时查询文件
        test_queries_file = "test_queries_temp.txt"
        test_queries = [
            "人工智能技术",
            "新能源汽车",
            "量子计算研究"
        ]
        
        with open(test_queries_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(test_queries))
        
        # 执行批量搜索
        output_file = f"test_batch_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        engine.batch_search(test_queries_file, output_file)
        
        # 清理临时文件
        if os.path.exists(test_queries_file):
            os.remove(test_queries_file)
        
        print(f"\n✅ 批量搜索测试完成")
        print(f"📁 结果文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 批量搜索测试失败: {e}")


def test_search_performance():
    """测试搜索性能"""
    print("\n🧪 测试搜索性能")
    print("=" * 50)
    
    try:
        engine = NewsSearchEngine()
        
        # 性能测试查询
        test_queries = [
            "人工智能",
            "新能源汽车",
            "量子计算",
            "经济政策",
            "科技创新"
        ]
        
        start_time = datetime.now()
        
        total_results = 0
        for query in test_queries:
            results = engine.search(query, top_k=5, threshold=0.3)
            total_results += len(results)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"📊 性能测试结果:")
        print(f"   查询数量: {len(test_queries)}")
        print(f"   总耗时: {duration:.2f} 秒")
        print(f"   平均耗时: {duration/len(test_queries):.2f} 秒/查询")
        print(f"   总结果数: {total_results}")
        print(f"   平均结果数: {total_results/len(test_queries):.1f}")
        
        print(f"\n✅ 性能测试完成")
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始新闻搜索功能全面测试")
    print("=" * 60)
    
    # 检查数据库是否存在
    if not os.path.exists("news_vectors"):
        print("❌ 向量数据库不存在，请先运行 csv_to_vector_processor.py")
        return
    
    # 运行各项测试
    test_database_stats()
    test_basic_search()
    test_advanced_search()
    test_search_performance()
    test_batch_search()
    
    print("\n🎉 所有测试完成！")
    print("💡 现在可以使用以下命令开始搜索:")
    print("   python news_search.py -i                    # 交互式搜索")
    print("   python news_search.py -q '人工智能' -t 5    # 单次搜索")
    print("   python news_search.py -s                    # 查看统计")


if __name__ == "__main__":
    run_all_tests()
