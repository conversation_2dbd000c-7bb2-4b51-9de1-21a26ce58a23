#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻搜索演示脚本
展示如何使用向量数据库进行新闻搜索
"""

import os
import sys
from typing import List, Dict
from vector_database import VectorDatabase
from config import SystemConfig

class NewsSearcher:
    """新闻搜索器"""
    
    def __init__(self):
        """初始化搜索器"""
        self.config = SystemConfig()
        self.vector_db = VectorDatabase(self.config.VECTOR_DB_PATH)
        
        print(f"📚 加载向量数据库: {self.config.VECTOR_DB_PATH}")
        print(f"   总新闻数: {len(self.vector_db.doc_ids)}")
        print(f"   向量维度: {self.vector_db.vector_dim}")
        print()
    
    def search_news(self, query: str, top_k: int = 5, threshold: float = 0.3) -> List[Dict]:
        """
        搜索相关新闻
        
        Args:
            query: 搜索查询
            top_k: 返回结果数量
            threshold: 相似度阈值
            
        Returns:
            相关新闻列表
        """
        print(f"🔍 搜索查询: {query}")
        print(f"   返回数量: {top_k}")
        print(f"   相似度阈值: {threshold}")
        print("-" * 50)
        
        try:
            results = self.vector_db.search(query, top_k=top_k, threshold=threshold)
            
            if not results:
                print("❌ 没有找到相关新闻")
                return []
            
            print(f"✅ 找到 {len(results)} 条相关新闻:")
            print()
            
            for i, result in enumerate(results, 1):
                similarity = result.get('similarity', 0)
                metadata = result.get('metadata', {})
                
                print(f"{i}. 相似度: {similarity:.3f}")
                print(f"   📰 {metadata.get('title', '无标题')}")
                print(f"   🏷️  {metadata.get('category', '未知')} | 📰 {metadata.get('media', '未知')}")
                print(f"   📅 {metadata.get('formatted_time', '未知时间')}")
                print(f"   🔗 {metadata.get('url', '无链接')}")
                
                # 显示内容摘要
                content = metadata.get('content', '')
                if content:
                    summary = content[:100] + "..." if len(content) > 100 else content
                    print(f"   📝 {summary}")
                
                print()
            
            return results
            
        except Exception as e:
            print(f"❌ 搜索出错: {e}")
            return []
    
    def get_news_by_category(self, category: str) -> List[Dict]:
        """按分类获取新闻"""
        print(f"📂 获取分类新闻: {category}")
        
        matching_news = []
        for doc_id in self.vector_db.doc_ids:
            metadata = self.vector_db.metadata.get(doc_id, {})
            if metadata.get('category', '') == category:
                matching_news.append({
                    'doc_id': doc_id,
                    'metadata': metadata
                })
        
        print(f"✅ 找到 {len(matching_news)} 条 '{category}' 分类新闻")
        return matching_news
    
    def get_news_by_media(self, media: str) -> List[Dict]:
        """按媒体来源获取新闻"""
        print(f"📺 获取媒体新闻: {media}")
        
        matching_news = []
        for doc_id in self.vector_db.doc_ids:
            metadata = self.vector_db.metadata.get(doc_id, {})
            if metadata.get('media', '') == media:
                matching_news.append({
                    'doc_id': doc_id,
                    'metadata': metadata
                })
        
        print(f"✅ 找到 {len(matching_news)} 条来自 '{media}' 的新闻")
        return matching_news
    
    def show_database_stats(self):
        """显示数据库统计信息"""
        stats = self.vector_db.get_stats()
        
        print("📊 数据库统计信息")
        print("=" * 50)
        print(f"总新闻数: {stats.get('total_documents', 0)}")
        print(f"向量维度: {stats.get('vector_dimension', 0)}")
        print(f"平均质量分: {stats.get('avg_quality_score', 0):.2f}")
        print(f"内存使用: {stats.get('memory_usage_mb', 0):.1f} MB")
        print()
        
        # 分类分布
        categories = stats.get('categories', {})
        if categories:
            print("📂 分类分布:")
            for category, count in categories.items():
                print(f"   {category}: {count} 条")
        print()
        
        # 来源分布
        sources = stats.get('sources', {})
        if sources:
            print("📺 来源分布:")
            for source, count in sources.items():
                print(f"   {source}: {count} 条")
        print()
        
        # 重复检测统计
        duplicate_stats = stats.get('duplicate_stats', {})
        if duplicate_stats:
            print("🔍 重复检测统计:")
            print(f"   重复URL: {duplicate_stats.get('duplicate_urls', 0)}")
            print(f"   重复内容: {duplicate_stats.get('duplicate_contents', 0)}")
            print(f"   唯一URL: {duplicate_stats.get('total_urls', 0)}")
            print(f"   唯一内容: {duplicate_stats.get('total_content_hashes', 0)}")
        print()

def main():
    """主函数 - 演示各种搜索功能"""
    searcher = NewsSearcher()
    
    # 显示数据库统计
    searcher.show_database_stats()
    
    # 演示搜索功能
    search_queries = [
        "经济发展",
        "科技创新", 
        "环境保护",
        "教育改革",
        "医疗健康"
    ]
    
    print("🔍 演示搜索功能")
    print("=" * 50)
    
    for query in search_queries:
        results = searcher.search_news(query, top_k=3, threshold=0.3)
        if results:
            print(f"✅ '{query}' 搜索完成\n")
        else:
            print(f"❌ '{query}' 没有找到相关结果\n")
    
    # 交互式搜索
    print("\n🎯 交互式搜索模式")
    print("=" * 50)
    print("输入搜索关键词，按回车搜索，输入 'quit' 退出")
    
    while True:
        try:
            query = input("\n🔍 请输入搜索关键词: ").strip()
            
            if query.lower() in ['quit', 'exit', '退出', 'q']:
                print("👋 再见！")
                break
            
            if not query:
                print("❌ 请输入有效的搜索关键词")
                continue
            
            results = searcher.search_news(query, top_k=5, threshold=0.2)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 出错了: {e}")

if __name__ == "__main__":
    main()
