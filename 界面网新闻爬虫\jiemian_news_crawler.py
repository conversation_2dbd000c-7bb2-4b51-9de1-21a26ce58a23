#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
界面新闻爬虫 - 爬取各分类当天新闻
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import csv
from datetime import datetime, date
import os
import logging
from urllib.parse import urljoin
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('jiemian_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class JiemianNewsCrawler:
    def __init__(self):
        self.base_url = "https://www.jiemian.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 分类信息 - 从HTML中提取的分类ID和名称
        self.categories = {
            65: "科技", 62: "地产", 51: "汽车", 31: "消费", 28: "工业",
            68: "时尚", 30: "交通", 472: "医药", 851: "互联网", 858: "创投",
            856: "能源", 853: "数码", 256: "教育", 845: "食品", 857: "新能源",
            850: "家电", 854: "健康", 676: "酒业", 841: "物流", 847: "零售",
            838: "美妆", 82: "体育", 694: "家居", 848: "餐饮", 846: "日用",
            852: "企服", 839: "珠宝", 840: "腕表", 605: "智库", 872: "电厂",
            883: "农业"
        }
        
        self.today = date.today().strftime("%Y-%m-%d")
        
    def get_page_content(self, url, max_retries=3):
        """获取页面内容"""
        for attempt in range(max_retries):
            try:
                response = self.session.get(url, timeout=10)
                response.raise_for_status()
                response.encoding = 'utf-8'
                return response.text
            except requests.RequestException as e:
                logging.warning(f"获取页面失败 (尝试 {attempt + 1}/{max_retries}): {url}, 错误: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                else:
                    logging.error(f"获取页面最终失败: {url}")
                    return None
    
    def parse_article_info(self, article_element):
        """解析文章信息 - 基于实际HTML结构"""
        try:
            # 查找标题 - 直接从h3获取文本
            title_element = article_element.find('h3', class_='card-list__title')
            if not title_element:
                return None

            title = title_element.get_text(strip=True)

            # 查找文章链接 - 从所有a标签中找到文章链接
            link = ""
            all_links = article_element.find_all('a')
            for link_element in all_links:
                href = link_element.get('href', '')
                if '/article/' in href and '.html' in href:
                    link = href
                    break

            if not link:
                return None

            if not title or len(title) < 5:  # 过滤掉太短的标题
                return None

            if link and not link.startswith('http'):
                link = urljoin(self.base_url, link)

            # 提取摘要
            summary = ""
            summary_element = article_element.find('div', class_='card-list__summary')
            if summary_element:
                summary_link = summary_element.find('a')
                if summary_link:
                    summary_p = summary_link.find('p')
                    if summary_p:
                        summary = summary_p.get_text(strip=True)

            # 提取作者和时间信息
            author = ""
            publish_time = ""
            views = ""

            # 查找footer信息
            footer_element = article_element.find('div', class_='news-footer')
            if footer_element:
                # 提取作者
                author_element = footer_element.find('span', class_='news-footer__author')
                if author_element:
                    author_links = author_element.find_all('a')
                    authors = []
                    for author_link in author_links:
                        author_name = author_link.get_text(strip=True)
                        if author_name:
                            authors.append(author_name)
                    author = ' '.join(authors) if authors else ""

                # 提取发布时间
                date_element = footer_element.find('span', class_='news-footer__date')
                if date_element:
                    publish_time = date_element.get_text(strip=True)

                # 提取阅读量
                right_element = footer_element.find('span', class_='news-footer__right')
                if right_element:
                    read_num_element = right_element.find('span', class_='news-footer__read-num')
                    if read_num_element:
                        em_element = read_num_element.find('em')
                        if em_element:
                            views = em_element.get_text(strip=True)

            return {
                'title': title,
                'link': link,
                'summary': summary,
                'author': author,
                'publish_time': publish_time,
                'views': views
            }
        except Exception as e:
            logging.warning(f"解析文章信息失败: {e}")
            return None
    
    def is_today_article(self, publish_time):
        """判断是否为当天文章"""
        if not publish_time:
            return False
            
        # 匹配今天、分钟前、小时前等
        today_patterns = [
            r'今天',
            r'\d+分钟前',
            r'\d+小时前',
            r'刚刚'
        ]
        
        for pattern in today_patterns:
            if re.search(pattern, publish_time):
                return True
        
        # 匹配今天的日期格式 MM/DD
        today_mmdd = datetime.now().strftime("%m/%d").lstrip('0').replace('/0', '/')
        if today_mmdd in publish_time:
            return True
            
        return False
    
    def crawl_category_news(self, category_id, category_name):
        """爬取指定分类的新闻"""
        url = f"{self.base_url}/lists/{category_id}.html"
        logging.info(f"开始爬取 {category_name} 分类新闻: {url}")
        
        html_content = self.get_page_content(url)
        if not html_content:
            return []
        
        soup = BeautifulSoup(html_content, 'html.parser')
        articles = []
        
        # 查找文章列表容器 - 基于实际HTML结构
        article_list_container = soup.find('ul', class_='card-list-items')
        if not article_list_container:
            # 备用查找方式
            article_list_container = soup.find('ul', id='load-list')

        if article_list_container:
            # 查找所有文章项
            article_items = article_list_container.find_all('li', class_='card-list')

            for item in article_items:
                article_info = self.parse_article_info(item)
                if article_info and article_info['title'] and self.is_today_article(article_info['publish_time']):
                    article_info['category'] = category_name
                    article_info['category_id'] = category_id
                    articles.append(article_info)
        else:
            logging.warning(f"未找到 {category_name} 分类的文章列表容器")
        
        logging.info(f"{category_name} 分类找到 {len(articles)} 篇当天文章")
        return articles

    def crawl_article_detail(self, article_url):
        """爬取单篇文章的详细内容"""
        try:
            logging.info(f"正在爬取文章详情: {article_url}")

            response = self.session.get(article_url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'

            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取文章详细信息
            article_detail = self.parse_article_detail(soup, article_url)

            if article_detail:
                logging.info(f"成功提取文章: {article_detail['title'][:50]}...")
                return article_detail
            else:
                logging.warning(f"无法解析文章内容: {article_url}")
                return None

        except Exception as e:
            logging.error(f"爬取文章详情失败 {article_url}: {e}")
            return None

    def parse_article_detail(self, soup, url):
        """解析文章详细内容"""
        try:
            article_detail = {
                'url': url,
                'title': '',
                'author': '',
                'publish_time': '',
                'source': '',
                'content': '',
                'images': []
            }

            # 提取标题 - 尝试多种选择器
            title_selectors = [
                'h1',
                '.article-title',
                '.title',
                'title'
            ]

            for selector in title_selectors:
                title_element = soup.select_one(selector)
                if title_element:
                    title_text = title_element.get_text(strip=True)
                    # 过滤掉包含网站名的标题
                    if '界面新闻' in title_text:
                        title_text = title_text.split('|')[0].strip()
                    if title_text and len(title_text) > 5:
                        article_detail['title'] = title_text
                        break

            # 提取作者、时间、来源信息
            # 查找包含作者信息的文本
            author_patterns = [
                r'([^·\s]+)\s*·\s*(\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2})\s*来源:([^·\n]+)',
                r'([^·\s]+)\s*·\s*来源:([^·\n]+)',
                r'界面新闻记者\s*\|\s*([^·\n]+)'
            ]

            page_text = soup.get_text()
            for pattern in author_patterns:
                match = re.search(pattern, page_text)
                if match:
                    if len(match.groups()) >= 3:
                        article_detail['author'] = match.group(1).strip()
                        article_detail['publish_time'] = match.group(2).strip()
                        article_detail['source'] = match.group(3).strip()
                    elif len(match.groups()) >= 2:
                        article_detail['author'] = match.group(1).strip()
                        article_detail['source'] = match.group(2).strip()
                    else:
                        article_detail['author'] = match.group(1).strip()
                    break

            # 提取正文内容
            content_parts = []

            # 查找文章正文容器
            content_selectors = [
                '.article-content',
                '.content',
                '.article-body',
                '.post-content',
                'article',
                '.main-content'
            ]

            content_container = None
            for selector in content_selectors:
                container = soup.select_one(selector)
                if container:
                    content_container = container
                    break

            if not content_container:
                # 如果没找到特定容器，尝试查找包含大量文本的div
                all_divs = soup.find_all('div')
                max_text_length = 0
                for div in all_divs:
                    text_length = len(div.get_text(strip=True))
                    if text_length > max_text_length:
                        max_text_length = text_length
                        content_container = div

            if content_container:
                # 提取段落文本
                paragraphs = content_container.find_all(['p', 'div'])
                for para in paragraphs:
                    text = para.get_text(strip=True)
                    # 过滤掉太短的段落和导航文本
                    if (len(text) > 20 and
                        not text.startswith('登录') and
                        not text.startswith('扫一扫') and
                        not text.startswith('Facebook') and
                        not text.startswith('微信') and
                        not text.startswith('版权所有') and
                        '界面新闻APP' not in text and
                        '未经正式授权严禁转载' not in text):
                        content_parts.append(text)

                # 如果段落提取效果不好，直接提取容器文本
                if len(content_parts) < 3:
                    full_text = content_container.get_text(strip=True)
                    # 按句号分割，过滤短句
                    sentences = [s.strip() for s in full_text.split('。') if len(s.strip()) > 20]
                    content_parts = sentences[:20]  # 限制句子数量

            article_detail['content'] = '\n\n'.join(content_parts)

            # 提取图片信息
            images = soup.find_all('img')
            for img in images:
                src = img.get('src', '')
                alt = img.get('alt', '')
                if src and 'http' in src:
                    article_detail['images'].append({
                        'src': src,
                        'alt': alt
                    })

            return article_detail

        except Exception as e:
            logging.error(f"解析文章详情失败: {e}")
            return None

    def crawl_articles_detail(self, articles):
        """批量爬取文章详细内容"""
        articles_detail = []

        logging.info(f"开始爬取 {len(articles)} 篇文章的详细内容")

        for i, article in enumerate(articles, 1):
            logging.info(f"处理第 {i}/{len(articles)} 篇文章")

            article_url = article.get('link', '')
            if not article_url:
                continue

            # 爬取详细内容
            detail = self.crawl_article_detail(article_url)
            if detail:
                # 合并基本信息和详细内容
                combined_article = {**article, **detail}
                articles_detail.append(combined_article)

            # 避免请求过于频繁
            time.sleep(1)

        logging.info(f"成功爬取 {len(articles_detail)} 篇文章的详细内容")
        return articles_detail
    
    def save_to_csv(self, all_articles, filename=None):
        """保存到CSV文件"""
        if not filename:
            filename = f"jiemian_news_{self.today}.csv"
        
        fieldnames = ['category', 'category_id', 'title', 'link', 'summary', 'author', 'publish_time', 'views']
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(all_articles)
        
        logging.info(f"数据已保存到 {filename}")
    
    def save_to_json(self, all_articles, filename=None):
        """保存到JSON文件"""
        if not filename:
            filename = f"jiemian_news_{self.today}.json"
        
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(all_articles, jsonfile, ensure_ascii=False, indent=2)
        
        logging.info(f"数据已保存到 {filename}")
    
    def crawl_all_categories(self, include_detail=False):
        """爬取所有分类的当天新闻

        Args:
            include_detail (bool): 是否包含详细内容
        """
        all_articles = []

        for category_id, category_name in self.categories.items():
            try:
                articles = self.crawl_category_news(category_id, category_name)
                all_articles.extend(articles)
                time.sleep(1)  # 避免请求过于频繁
            except Exception as e:
                logging.error(f"爬取 {category_name} 分类失败: {e}")
                continue

        logging.info(f"总共爬取到 {len(all_articles)} 篇当天文章")

        # 保存基础数据
        if all_articles:
            self.save_to_csv(all_articles)
            self.save_to_json(all_articles)

        # 如果需要详细内容，继续爬取
        if include_detail and all_articles:
            print("\n开始爬取文章详细内容...")
            detailed_articles = self.crawl_articles_detail(all_articles)

            # 保存详细内容
            if detailed_articles:
                detailed_filename = f"jiemian_news_{self.today}_detailed.json"
                with open(detailed_filename, 'w', encoding='utf-8') as f:
                    json.dump(detailed_articles, f, ensure_ascii=False, indent=2)
                logging.info(f"详细内容已保存到 {detailed_filename}")

                return detailed_articles

        return all_articles

def main():
    """主函数"""
    crawler = JiemianNewsCrawler()

    print("界面新闻爬虫 - 完整内容爬取")
    print("=" * 50)
    print(f"目标日期: {crawler.today}")
    print(f"分类数量: {len(crawler.categories)}")
    print("正在爬取所有文章的完整内容（包含正文、图片等详细信息）...")
    print("注意：爬取详细内容需要更多时间，请耐心等待...")

    # 直接爬取完整内容
    articles = crawler.crawl_all_categories(include_detail=True)

    print(f"\n爬取完成！")
    print(f"总共获取 {len(articles)} 篇文章的完整内容")

    # 按分类统计
    category_stats = {}
    for article in articles:
        category = article['category']
        category_stats[category] = category_stats.get(category, 0) + 1

    print("\n各分类文章数量:")
    for category, count in sorted(category_stats.items()):
        print(f"  {category}: {count} 篇")

    # 输出文件信息
    print(f"\n输出文件:")
    print(f"  - jiemian_news_{crawler.today}.csv (基础信息)")
    print(f"  - jiemian_news_{crawler.today}.json (基础信息)")
    print(f"  - jiemian_news_{crawler.today}_detailed.json (详细内容)")

if __name__ == "__main__":
    main()
