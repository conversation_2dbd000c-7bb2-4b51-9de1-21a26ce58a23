#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
界面新闻爬虫适配器
将界面新闻爬虫的数据格式转换为统一标准格式
"""

import sys
import os
from datetime import datetime
from jiemian_news_crawler import JiemianNewsCrawler


class JiemianNewsAdapter:
    """界面新闻适配器"""
    
    def __init__(self, max_news=50, get_detail=True, get_all=False):
        """
        初始化适配器
        :param max_news: 最大新闻数量
        :param get_detail: 是否获取详细内容
        :param get_all: 是否获取全部新闻
        """
        self.max_news = max_news
        self.get_detail = get_detail
        self.get_all = get_all
        self.crawler = JiemianNewsCrawler()
    
    def crawl(self):
        """
        执行爬取并返回标准格式数据
        :return: (news_list, count) 元组
        """
        # 静默爬取，不输出过程信息
        raw_news_list = self.crawler.crawl_all_categories(include_detail=self.get_detail)

        if not raw_news_list:
            return [], 0

        # 限制数量
        if not self.get_all and len(raw_news_list) > self.max_news:
            raw_news_list = raw_news_list[:self.max_news]

        # 转换为标准格式
        standard_news_list = []
        for i, raw_item in enumerate(raw_news_list, 1):
            standard_item = self.convert_to_standard_format(raw_item, i)
            if standard_item:
                standard_news_list.append(standard_item)
        return standard_news_list, len(standard_news_list)
    
    def convert_to_standard_format(self, raw_item, rank):
        """
        将界面网原始数据转换为标准格式
        原始格式: {'category': '...', 'category_id': ..., 'title': '...', 'link': '...', 
                  'summary': '...', 'author': '...', 'publish_time': '...', 'views': '...'}
        """
        try:
            # 解析时间
            create_date, create_time, formatted_time = self.parse_time(raw_item.get('publish_time', ''))
            
            # 获取分类
            category = raw_item.get('category', '综合')
            
            # 构建内容（摘要作为内容）
            content = raw_item.get('summary', '')
            if raw_item.get('author'):
                content = f"作者: {raw_item['author']}\n{content}"
            
            return {
                'id': rank,
                'title': raw_item.get('title', '').strip(),
                'url': raw_item.get('link', ''),
                'media': '界面新闻',
                'create_date': create_date,
                'create_time': create_time,
                'formatted_time': formatted_time,
                'category': category,
                'ranking_type': '分类新闻',
                'rank': rank,
                'comment_url': '',
                'top_num': raw_item.get('views', ''),
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'content': content.strip()[:2000]  # 限制长度
            }
        except Exception as e:
            print(f"[界面网适配器] 转换数据失败: {e}")
            return None
    
    def parse_time(self, time_str):
        """解析时间字符串"""
        try:
            if not time_str:
                now = datetime.now()
                return now.strftime('%Y-%m-%d'), now.strftime('%H:%M:%S'), now.strftime('%m-%d %H:%M')
            
            # 处理各种时间格式
            time_str = time_str.strip()
            
            # 格式1: 2024-07-07 15:30:00
            if '-' in time_str and ':' in time_str:
                try:
                    if len(time_str.split()) == 2:  # 有日期和时间
                        # 尝试完整格式
                        try:
                            dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                        except:
                            dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M')
                        return dt.strftime('%Y-%m-%d'), dt.strftime('%H:%M:%S'), dt.strftime('%m-%d %H:%M')
                    else:  # 只有日期
                        dt = datetime.strptime(time_str, '%Y-%m-%d')
                        return dt.strftime('%Y-%m-%d'), '00:00:00', dt.strftime('%m-%d')
                except:
                    pass
            
            # 格式2: 07-07 15:30 (当年)
            if time_str.count('-') == 1 and ':' in time_str:
                current_year = datetime.now().year
                full_time_str = f"{current_year}-{time_str}"
                try:
                    dt = datetime.strptime(full_time_str, '%Y-%m-%d %H:%M')
                    return dt.strftime('%Y-%m-%d'), dt.strftime('%H:%M:%S'), dt.strftime('%m-%d %H:%M')
                except:
                    pass
            
            # 格式3: 15:30 (今天的时间)
            if ':' in time_str and len(time_str.split(':')) == 2:
                try:
                    now = datetime.now()
                    hour, minute = time_str.split(':')
                    dt = now.replace(hour=int(hour), minute=int(minute), second=0)
                    return dt.strftime('%Y-%m-%d'), dt.strftime('%H:%M:%S'), dt.strftime('%m-%d %H:%M')
                except:
                    pass
            
            # 格式4: 相对时间 (1小时前, 2天前等)
            if '前' in time_str:
                now = datetime.now()
                if '小时前' in time_str:
                    hours = int(time_str.replace('小时前', '').strip())
                    from datetime import timedelta
                    dt = now - timedelta(hours=hours)
                    return dt.strftime('%Y-%m-%d'), dt.strftime('%H:%M:%S'), dt.strftime('%m-%d %H:%M')
                elif '天前' in time_str:
                    days = int(time_str.replace('天前', '').strip())
                    from datetime import timedelta
                    dt = now - timedelta(days=days)
                    return dt.strftime('%Y-%m-%d'), dt.strftime('%H:%M:%S'), dt.strftime('%m-%d %H:%M')
            
            # 默认使用当前时间
            now = datetime.now()
            return now.strftime('%Y-%m-%d'), now.strftime('%H:%M:%S'), now.strftime('%m-%d %H:%M')
            
        except Exception as e:
            print(f"[界面网适配器] 时间解析失败: {e}, 原始时间: {time_str}")
            now = datetime.now()
            return now.strftime('%Y-%m-%d'), now.strftime('%H:%M:%S'), now.strftime('%m-%d %H:%M')


def main():
    """测试适配器"""
    adapter = JiemianNewsAdapter(max_news=10, get_detail=True, get_all=False)
    news_list, count = adapter.crawl()
    
    print(f"\n=== 界面网适配器测试结果 ===")
    print(f"获取新闻数量: {count}")
    
    if news_list:
        print(f"\n前3条新闻示例:")
        for i, news in enumerate(news_list[:3], 1):
            print(f"\n{i}. {news['title']}")
            print(f"   媒体: {news['media']}")
            print(f"   分类: {news['category']}")
            print(f"   时间: {news['formatted_time']}")
            print(f"   内容: {news['content'][:100]}...")


if __name__ == "__main__":
    main()
