#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一新闻爬虫 - 双线程同时爬取网易和新浪新闻
合并输出为统一格式的CSV文件
"""

import threading
import time
import csv
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import sys
import os

# 导入现有的爬虫类
from netease_news_crawler import NeteaseNewsCrawler
from sina_news_crawler import SinaNewsCrawler
from ifeng_news_adapter import IfengNewsAdapter
from jiemian_news_adapter import JiemianNewsAdapter


class UnifiedNewsCrawler:
    """统一新闻爬虫管理器"""

    def __init__(self, max_news_per_source=50, get_detail=True, get_all=False):
        """
        初始化统一爬虫
        :param max_news_per_source: 每个来源最大新闻数量，如果get_all=True则忽略此参数
        :param get_detail: 是否获取详细内容
        :param get_all: 是否获取所有新闻
        """
        self.max_news_per_source = max_news_per_source
        self.get_detail = get_detail
        self.get_all = get_all
        self.results = {
            'netease': [],
            'sina': [],
            'ifeng': [],
            'jiemian': []
        }
        self.errors = {}
        self.start_time = datetime.now()
        
    def crawl_netease(self):
        """爬取网易新闻"""
        try:
            print(f"  📰 [线程1-网易] 开始爬取网易新闻...")
            crawler = NeteaseNewsCrawler(
                max_news=self.max_news_per_source,
                get_detail=self.get_detail,
                get_all=self.get_all
            )

            print(f"  📰 [线程1-网易] 正在获取新闻首页...")
            # 使用不保存文件的方法
            news_items = crawler.crawl_without_save()
            print(f"  📰 [线程1-网易] 成功获取 {len(news_items)} 条新闻")

            # 立即显示每条新闻的详细信息
            for i, news in enumerate(news_items, 1):
                print(f"  📋 [线程1-网易] 第{i}条: {news['title']}")
                print(f"      🔗 {news['url']}")
                print(f"      📅 {news['formatted_time']} | 🏷️ {news['category']}")
                if news.get('content') and news['content'].strip():
                    content_preview = news['content'][:50] + "..." if len(news['content']) > 50 else news['content']
                    print(f"      📝 {content_preview}")
                print(f"      {'-'*40}")

            self.results['netease'] = news_items

        except Exception as e:
            print(f"  ❌ [线程1-网易] 爬取失败: {e}")
            self.results['netease'] = []
            self.errors['netease'] = str(e)
    
    def crawl_sina(self):
        """爬取新浪新闻"""
        try:
            print(f"  📰 [线程2-新浪] 开始爬取新浪新闻...")
            crawler = SinaNewsCrawler()

            if self.get_all:
                limit_per_category = 20
            else:
                limit_per_category = max(5, self.max_news_per_source // 6)  # 6个主要分类

            print(f"  📰 [线程2-新浪] 正在获取各类排行榜数据...")
            # 获取所有排行榜数据
            all_rankings = crawler.get_all_rankings(
                limit=limit_per_category,
                get_content=False  # 先不获取详细内容
            )

            # 转换为统一格式
            unified_news = []
            for category, rankings in all_rankings.items():
                for ranking_type, news_list in rankings.items():
                    for item in news_list:
                        unified_item = self.convert_sina_to_unified_format(item, category, ranking_type)
                        if unified_item:
                            unified_news.append(unified_item)

            # 去重（基于URL）
            seen_urls = set()
            unique_news = []
            for item in unified_news:
                if item['url'] not in seen_urls:
                    unique_news.append(item)
                    seen_urls.add(item['url'])

            # 如果设置了具体数量限制，则截取
            if not self.get_all and len(unique_news) > self.max_news_per_source:
                unique_news = unique_news[:self.max_news_per_source]

            # 如果需要获取详细内容
            if self.get_detail and unique_news:
                print(f"  📰 [线程2-新浪] 正在获取 {len(unique_news)} 条新闻的详细内容...")
                for i, item in enumerate(unique_news):
                    try:
                        if (i + 1) % 10 == 0:  # 每10条显示一次进度
                            print(f"  📰 [线程2-新浪] 已处理 {i + 1}/{len(unique_news)} 条新闻...")
                        content = crawler._fetch_article_content(item['url'])
                        if content:
                            item['content'] = content[:2000]  # 限制长度
                        else:
                            item['content'] = ""
                    except Exception as e:
                        item['content'] = ""
                    time.sleep(0.2)  # 减少延时，提高并行效率

            print(f"  📰 [线程2-新浪] 成功获取 {len(unique_news)} 条新闻")

            # 立即显示每条新闻的详细信息
            for i, news in enumerate(unique_news, 1):
                print(f"  📋 [线程2-新浪] 第{i}条: {news['title']}")
                print(f"      🔗 {news['url']}")
                print(f"      📅 {news['formatted_time']} | 🏷️ {news['category']}")
                if news.get('content') and news['content'].strip():
                    content_preview = news['content'][:50] + "..." if len(news['content']) > 50 else news['content']
                    print(f"      📝 {content_preview}")
                print(f"      {'-'*40}")

            self.results['sina'] = unique_news

        except Exception as e:
            print(f"  ❌ [线程2-新浪] 爬取失败: {e}")
            self.results['sina'] = []
            self.errors['sina'] = str(e)

    def crawl_ifeng(self):
        """爬取凤凰网新闻"""
        try:
            print(f"  📰 [线程3-凤凰] 开始爬取凤凰网新闻...")
            adapter = IfengNewsAdapter(
                max_news=self.max_news_per_source,
                get_detail=self.get_detail,
                get_all=self.get_all
            )
            print(f"  📰 [线程3-凤凰] 正在获取热点新闻...")
            news_list, count = adapter.crawl()
            print(f"  📰 [线程3-凤凰] 成功获取 {len(news_list)} 条新闻")

            # 立即显示每条新闻的详细信息
            for i, news in enumerate(news_list, 1):
                print(f"  📋 [线程3-凤凰] 第{i}条: {news['title']}")
                print(f"      🔗 {news['url']}")
                print(f"      📅 {news['formatted_time']} | 🏷️ {news['category']}")
                if news.get('content') and news['content'].strip():
                    content_preview = news['content'][:50] + "..." if len(news['content']) > 50 else news['content']
                    print(f"      📝 {content_preview}")
                print(f"      {'-'*40}")

            self.results['ifeng'] = news_list
        except Exception as e:
            print(f"  ❌ [线程3-凤凰] 爬取失败: {e}")
            self.results['ifeng'] = []
            self.errors['ifeng'] = str(e)

    def crawl_jiemian(self):
        """爬取界面新闻"""
        try:
            print(f"  📰 [线程4-界面] 开始爬取界面新闻...")
            adapter = JiemianNewsAdapter(
                max_news=self.max_news_per_source,
                get_detail=self.get_detail,
                get_all=self.get_all
            )
            print(f"  📰 [线程4-界面] 正在获取各分类新闻...")
            news_list, count = adapter.crawl()
            print(f"  📰 [线程4-界面] 成功获取 {len(news_list)} 条新闻")

            # 立即显示每条新闻的详细信息
            for i, news in enumerate(news_list, 1):
                print(f"  📋 [线程4-界面] 第{i}条: {news['title']}")
                print(f"      🔗 {news['url']}")
                print(f"      📅 {news['formatted_time']} | 🏷️ {news['category']}")
                if news.get('content') and news['content'].strip():
                    content_preview = news['content'][:50] + "..." if len(news['content']) > 50 else news['content']
                    print(f"      📝 {content_preview}")
                print(f"      {'-'*40}")

            self.results['jiemian'] = news_list
        except Exception as e:
            print(f"  ❌ [线程4-界面] 爬取失败: {e}")
            self.results['jiemian'] = []
            self.errors['jiemian'] = str(e)
    
    def convert_sina_to_unified_format(self, sina_item, ranking_type, sub_type):
        """将新浪新闻数据转换为统一格式"""
        try:
            # 生成唯一ID
            news_id = len(self.results['sina']) + len(self.results['netease']) + 1

            # 新浪爬虫返回的数据格式：{'序号': 1, '标题': '...', '链接': '...', '媒体': '...', '时间': '...', '内容': '...'}
            title = sina_item.get('标题', sina_item.get('title', ''))
            url = sina_item.get('链接', sina_item.get('url', ''))
            media = sina_item.get('媒体', sina_item.get('media', '新浪新闻'))
            time_str = sina_item.get('时间', sina_item.get('time', ''))
            content = sina_item.get('内容', sina_item.get('content', ''))
            rank = sina_item.get('序号', sina_item.get('rank', news_id))

            # 解析时间
            create_date = ''
            create_time = ''
            formatted_time = ''

            if time_str:
                try:
                    # 尝试解析不同的时间格式
                    if '-' in time_str and ':' in time_str:
                        # 格式：2024-06-28 15:30:00
                        dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                        create_date = dt.strftime('%Y-%m-%d')
                        create_time = dt.strftime('%H:%M:%S')
                        formatted_time = dt.strftime('%m-%d %H:%M')
                    elif '-' in time_str:
                        # 格式：2024-06-28
                        dt = datetime.strptime(time_str, '%Y-%m-%d')
                        create_date = dt.strftime('%Y-%m-%d')
                        create_time = '00:00:00'
                        formatted_time = dt.strftime('%m-%d')
                    else:
                        # 其他格式，使用当前时间
                        now = datetime.now()
                        create_date = now.strftime('%Y-%m-%d')
                        create_time = now.strftime('%H:%M:%S')
                        formatted_time = now.strftime('%m-%d %H:%M')
                except:
                    # 解析失败，使用当前时间
                    now = datetime.now()
                    create_date = now.strftime('%Y-%m-%d')
                    create_time = now.strftime('%H:%M:%S')
                    formatted_time = now.strftime('%m-%d %H:%M')
            else:
                # 没有时间信息，使用当前时间
                now = datetime.now()
                create_date = now.strftime('%Y-%m-%d')
                create_time = now.strftime('%H:%M:%S')
                formatted_time = now.strftime('%m-%d %H:%M')

            return {
                'id': news_id,
                'title': title,
                'url': url,
                'media': media,
                'create_date': create_date,
                'create_time': create_time,
                'formatted_time': formatted_time,
                'category': ranking_type,
                'ranking_type': sub_type,
                'rank': rank,
                'comment_url': '',  # 新浪爬虫暂时没有评论链接
                'top_num': '',      # 新浪爬虫暂时没有热度数字
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'content': content[:2000] if content else ''  # 限制内容长度
            }
        except Exception as e:
            print(f"转换新浪数据格式失败: {e}")
            return None
    
    def merge_and_get_results(self):
        """合并结果并返回数据"""
        # 合并所有结果
        all_news = []
        
        # 添加网易新闻
        for item in self.results['netease']:
            all_news.append(item)
        
        # 添加新浪新闻
        for item in self.results['sina']:
            all_news.append(item)

        # 添加凤凰网新闻
        for item in self.results['ifeng']:
            all_news.append(item)

        # 添加界面新闻
        for item in self.results['jiemian']:
            all_news.append(item)
        
        # 重新分配ID
        for i, item in enumerate(all_news, 1):
            item['id'] = i
            item['rank'] = i

        # 显示详细统计信息
        print(f"\n📊 爬取结果统计:")
        print(f"{'='*80}")

        total_success = 0
        for source, chinese_name in [('netease', '网易新闻'), ('sina', '新浪新闻'),
                                   ('ifeng', '凤凰网'), ('jiemian', '界面新闻')]:
            count = len(self.results[source])
            total_success += count
            status = "✅" if count > 0 else "❌"
            error_info = f" (错误: {self.errors.get(source, '')})" if source in self.errors else ""
            print(f"  {status} {chinese_name}: {count} 条{error_info}")

        print(f"{'='*80}")
        print(f"🎯 总计成功: {total_success} 条新闻")
        print(f"⏱️  总耗时: {(datetime.now() - self.start_time).total_seconds():.1f} 秒")
        print(f"📈 平均速度: {total_success / max(1, (datetime.now() - self.start_time).total_seconds()):.1f} 条/秒")

        if self.errors:
            print(f"⚠️  发生错误的源: {len(self.errors)} 个")

        print(f"{'='*80}")

        # 保存为CSV文件
        filename = self.save_to_csv(all_news)

        return filename, len(all_news), all_news
    
    def crawl_all(self):
        """使用多线程同时爬取所有新闻"""
        print(f"\n🚀 开始四线程并发爬取新闻...")
        if self.get_all:
            print(f"📋 设置: 获取全部新闻, 获取详细内容: {'是' if self.get_detail else '否'}")
        else:
            print(f"📋 设置: 每个来源最多 {self.max_news_per_source} 条, 获取详细内容: {'是' if self.get_detail else '否'}")
        print(f"{'='*80}")

        # 使用线程池执行，确保真正并行
        import threading
        import time

        # 创建线程
        sources = ['网易新闻', '新浪新闻', '凤凰网', '界面新闻']
        threads = [
            threading.Thread(target=self.crawl_netease, name="NetEase-Thread"),
            threading.Thread(target=self.crawl_sina, name="Sina-Thread"),
            threading.Thread(target=self.crawl_ifeng, name="Ifeng-Thread"),
            threading.Thread(target=self.crawl_jiemian, name="Jiemian-Thread")
        ]

        # 启动所有线程
        print(f"⚡ 启动4个并发线程...")
        for i, thread in enumerate(threads):
            print(f"  🔄 启动线程{i+1}: {sources[i]}")
            thread.start()
            time.sleep(0.1)  # 稍微错开启动时间

        # 等待所有线程完成，并显示进度
        print(f"\n⏳ 等待所有线程完成...")
        completed = [False] * 4

        while not all(completed):
            for i, thread in enumerate(threads):
                if not completed[i] and not thread.is_alive():
                    completed[i] = True
                    print(f"  ✅ {sources[i]} 爬取完成")
            time.sleep(0.5)

        # 确保所有线程都已结束
        for thread in threads:
            thread.join()

        print(f"🎉 所有线程执行完毕！")

        # 合并并返回结果
        return self.merge_and_get_results()

    def save_to_csv(self, all_news):
        """保存为CSV文件"""
        if not all_news:
            return None

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"unified_news_{timestamp}.csv"

        try:
            # 使用标准字段名
            fieldnames = [
                'id', 'title', 'url', 'media', 'create_date', 'create_time',
                'formatted_time', 'category', 'ranking_type', 'rank',
                'comment_url', 'top_num', 'crawl_time', 'content'
            ]

            with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(all_news)

            return filename

        except Exception as e:
            print(f"❌ 保存CSV文件失败: {e}")
            return None


def main():
    """主函数"""
    # 默认参数
    max_news_per_source = 30
    get_detail = True
    get_all = False

    # 解析命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1].lower() in ['all', 'a', '全部']:
            get_all = True
            max_news_per_source = 999  # 设置一个大数值
        else:
            try:
                max_news_per_source = int(sys.argv[1])
            except ValueError:
                print("错误：新闻数量必须是数字，或使用 'all' 获取全部新闻")
                return

    if len(sys.argv) > 2:
        get_detail = sys.argv[2].lower() in ['true', '1', 'yes', 'y']

    # 创建统一爬虫
    crawler = UnifiedNewsCrawler(
        max_news_per_source=max_news_per_source,
        get_detail=get_detail,
        get_all=get_all
    )
    
    # 开始爬取
    filename, total_count, all_news = crawler.crawl_all()

    if filename and total_count > 0:
        print(f"✅ 统一新闻数据已保存到: {filename}")

        # 显示每条新闻的详细信息
        print(f"\n📰 新闻详细信息:")
        print(f"{'='*80}")

        for i, news in enumerate(all_news, 1):
            print(f"\n📋 第 {i} 条新闻:")
            print(f"  🏷️  标题: {news['title']}")
            print(f"  🔗 链接: {news['url']}")
            print(f"  📰 媒体: {news['media']}")
            print(f"  📅 时间: {news['formatted_time']}")
            print(f"  🏷️  分类: {news['category']}")
            if news.get('content') and news['content'].strip():
                content_preview = news['content'][:100] + "..." if len(news['content']) > 100 else news['content']
                print(f"  📝 内容: {content_preview}")
            print(f"  {'-'*60}")

        print(f"\n🎉 爬取任务完成！")
        print(f"📁 数据文件: {filename}")
        print(f"📊 新闻总数: {total_count} 条")
        print(f"\n📋 下一步操作:")
        print(f"  1️⃣  处理向量数据: python csv_to_vector_processor.py {filename}")
        print(f"  2️⃣  搜索新闻: python news_search.py")
        print(f"\n💡 使用说明:")
        print(f"  python unified_news_crawler.py [每个来源数量|all] [获取详细内容]")
        print(f"  例如: python unified_news_crawler.py 50 true")
    else:
        print(f"\n❌ 爬取失败或未获取到新闻数据")
        print(f"请检查网络连接或稍后重试")


if __name__ == "__main__":
    main()
