#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一新闻爬虫 - 双线程同时爬取网易和新浪新闻
合并输出为统一格式的CSV文件
"""

import threading
import time
import csv
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import sys
import os

# 导入现有的爬虫类
from netease_news_crawler import NeteaseNewsCrawler
from sina_news_crawler import SinaNewsCrawler


class UnifiedNewsCrawler:
    """统一新闻爬虫管理器"""

    def __init__(self, max_news_per_source=50, get_detail=True, get_all=False):
        """
        初始化统一爬虫
        :param max_news_per_source: 每个来源最大新闻数量，如果get_all=True则忽略此参数
        :param get_detail: 是否获取详细内容
        :param get_all: 是否获取所有新闻
        """
        self.max_news_per_source = max_news_per_source
        self.get_detail = get_detail
        self.get_all = get_all
        self.results = {
            'netease': [],
            'sina': []
        }
        self.start_time = datetime.now()
        
    def crawl_netease(self):
        """爬取网易新闻"""
        try:
            print(f"[线程1] 开始爬取网易新闻...")
            # 直接使用网易爬虫的crawl方法，它内部会处理详细内容获取
            crawler = NeteaseNewsCrawler(
                max_news=self.max_news_per_source,
                get_detail=self.get_detail,
                get_all=self.get_all
            )

            # 使用crawl方法，它会自动处理详细内容获取
            news_items = crawler.crawl()

            self.results['netease'] = news_items
            print(f"[线程1] 网易新闻爬取完成，共 {len(news_items)} 条")
            
        except Exception as e:
            print(f"[线程1] 网易新闻爬取失败: {e}")
            self.results['netease'] = []
    
    def crawl_sina(self):
        """爬取新浪新闻"""
        try:
            print(f"[线程2] 开始爬取新浪新闻...")
            crawler = SinaNewsCrawler()

            # 使用get_all_rankings获取更多新闻
            print(f"[线程2] 正在获取新浪各类新闻...")
            if self.get_all:
                # 获取所有分类的新闻，每个分类20条
                limit_per_category = 20
                print(f"[线程2] 将获取全部新浪新闻（每个分类{limit_per_category}条）")
            else:
                # 计算每个分类应该获取多少条
                limit_per_category = max(5, self.max_news_per_source // 6)  # 6个主要分类
                print(f"[线程2] 将获取新浪新闻（每个分类{limit_per_category}条，总计约{limit_per_category * 6}条）")

            # 获取所有排行榜数据
            all_rankings = crawler.get_all_rankings(
                limit=limit_per_category,
                get_content=False  # 先不获取详细内容
            )

            # 转换为统一格式
            unified_news = []
            for category, rankings in all_rankings.items():
                for ranking_type, news_list in rankings.items():
                    for item in news_list:
                        unified_item = self.convert_sina_to_unified_format(item, category, ranking_type)
                        if unified_item:
                            unified_news.append(unified_item)

            # 去重（基于URL）
            seen_urls = set()
            unique_news = []
            for item in unified_news:
                if item['url'] not in seen_urls:
                    unique_news.append(item)
                    seen_urls.add(item['url'])

            # 如果设置了具体数量限制，则截取
            if not self.get_all and len(unique_news) > self.max_news_per_source:
                unique_news = unique_news[:self.max_news_per_source]

            # 如果需要获取详细内容，单独处理
            if self.get_detail and unique_news:
                print(f"[线程2] 开始获取新浪新闻详细内容...")
                for i, item in enumerate(unique_news):
                    print(f"[线程2] 新浪新闻 {i+1}/{len(unique_news)}")
                    try:
                        # 使用新浪爬虫的内容获取方法
                        content = crawler._fetch_article_content(item['url'])
                        if content:
                            item['content'] = content[:2000]  # 限制长度
                        else:
                            item['content'] = ""
                    except Exception as e:
                        print(f"[线程2] 获取新浪新闻内容失败: {e}")
                        item['content'] = ""
                    time.sleep(0.2)  # 减少延时，提高并行效率

            self.results['sina'] = unique_news
            print(f"[线程2] 新浪新闻爬取完成，共 {len(unique_news)} 条")

        except Exception as e:
            print(f"[线程2] 新浪新闻爬取失败: {e}")
            self.results['sina'] = []
    
    def convert_sina_to_unified_format(self, sina_item, ranking_type, sub_type):
        """将新浪新闻数据转换为统一格式"""
        try:
            # 生成唯一ID
            news_id = len(self.results['sina']) + len(self.results['netease']) + 1

            # 新浪爬虫返回的数据格式：{'序号': 1, '标题': '...', '链接': '...', '媒体': '...', '时间': '...', '内容': '...'}
            title = sina_item.get('标题', sina_item.get('title', ''))
            url = sina_item.get('链接', sina_item.get('url', ''))
            media = sina_item.get('媒体', sina_item.get('media', '新浪新闻'))
            time_str = sina_item.get('时间', sina_item.get('time', ''))
            content = sina_item.get('内容', sina_item.get('content', ''))
            rank = sina_item.get('序号', sina_item.get('rank', news_id))

            # 解析时间
            create_date = ''
            create_time = ''
            formatted_time = ''

            if time_str:
                try:
                    # 尝试解析不同的时间格式
                    if '-' in time_str and ':' in time_str:
                        # 格式：2024-06-28 15:30:00
                        dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                        create_date = dt.strftime('%Y-%m-%d')
                        create_time = dt.strftime('%H:%M:%S')
                        formatted_time = dt.strftime('%m-%d %H:%M')
                    elif '-' in time_str:
                        # 格式：2024-06-28
                        dt = datetime.strptime(time_str, '%Y-%m-%d')
                        create_date = dt.strftime('%Y-%m-%d')
                        create_time = '00:00:00'
                        formatted_time = dt.strftime('%m-%d')
                    else:
                        # 其他格式，使用当前时间
                        now = datetime.now()
                        create_date = now.strftime('%Y-%m-%d')
                        create_time = now.strftime('%H:%M:%S')
                        formatted_time = now.strftime('%m-%d %H:%M')
                except:
                    # 解析失败，使用当前时间
                    now = datetime.now()
                    create_date = now.strftime('%Y-%m-%d')
                    create_time = now.strftime('%H:%M:%S')
                    formatted_time = now.strftime('%m-%d %H:%M')
            else:
                # 没有时间信息，使用当前时间
                now = datetime.now()
                create_date = now.strftime('%Y-%m-%d')
                create_time = now.strftime('%H:%M:%S')
                formatted_time = now.strftime('%m-%d %H:%M')

            return {
                'id': news_id,
                'title': title,
                'url': url,
                'media': media,
                'create_date': create_date,
                'create_time': create_time,
                'formatted_time': formatted_time,
                'category': ranking_type,
                'ranking_type': sub_type,
                'rank': rank,
                'comment_url': '',  # 新浪爬虫暂时没有评论链接
                'top_num': '',      # 新浪爬虫暂时没有热度数字
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'content': content[:2000] if content else ''  # 限制内容长度
            }
        except Exception as e:
            print(f"转换新浪数据格式失败: {e}")
            return None
    
    def merge_and_get_results(self):
        """合并结果并返回数据"""
        # 合并所有结果
        all_news = []
        
        # 添加网易新闻
        for item in self.results['netease']:
            all_news.append(item)
        
        # 添加新浪新闻
        for item in self.results['sina']:
            all_news.append(item)
        
        # 重新分配ID
        for i, item in enumerate(all_news, 1):
            item['id'] = i
            item['rank'] = i

        print(f"\n{'='*80}")
        print(f"合并完成！")
        print(f"网易新闻: {len(self.results['netease'])} 条")
        print(f"新浪新闻: {len(self.results['sina'])} 条")
        print(f"总计: {len(all_news)} 条新闻")
        print(f"总耗时: {(datetime.now() - self.start_time).total_seconds():.1f} 秒")
        print(f"{'='*80}")

        return all_news, len(all_news)
    
    def crawl_all(self):
        """使用双线程同时爬取所有新闻"""
        print(f"开始双线程爬取新闻...")
        if self.get_all:
            print(f"设置: 获取全部新闻, 获取详细内容: {'是' if self.get_detail else '否'}")
        else:
            print(f"设置: 每个来源最多 {self.max_news_per_source} 条, 获取详细内容: {'是' if self.get_detail else '否'}")
        print(f"{'='*80}")

        # 使用线程池执行，确保真正并行
        import threading

        # 创建线程
        thread1 = threading.Thread(target=self.crawl_netease, name="NetEase-Thread")
        thread2 = threading.Thread(target=self.crawl_sina, name="Sina-Thread")

        # 启动线程
        thread1.start()
        thread2.start()

        # 等待两个线程完成
        thread1.join()
        thread2.join()

        # 合并并返回结果
        return self.merge_and_get_results()


def main():
    """主函数"""
    # 默认参数
    max_news_per_source = 30
    get_detail = True
    get_all = False

    # 解析命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1].lower() in ['all', 'a', '全部']:
            get_all = True
            max_news_per_source = 999  # 设置一个大数值
        else:
            try:
                max_news_per_source = int(sys.argv[1])
            except ValueError:
                print("错误：新闻数量必须是数字，或使用 'all' 获取全部新闻")
                return

    if len(sys.argv) > 2:
        get_detail = sys.argv[2].lower() in ['true', '1', 'yes', 'y']

    # 创建统一爬虫
    crawler = UnifiedNewsCrawler(
        max_news_per_source=max_news_per_source,
        get_detail=get_detail,
        get_all=get_all
    )
    
    # 开始爬取
    filename, total_count = crawler.crawl_all()
    
    if filename and total_count > 0:
        print(f"\n爬取成功！")
        print(f"使用说明：")
        print(f"python unified_news_crawler.py [每个来源新闻数量|all] [是否获取详细内容]")
        print(f"例如：")
        print(f"  python unified_news_crawler.py 50 true    # 每个来源50条，包含详细内容")
        print(f"  python unified_news_crawler.py 30 false   # 每个来源30条，不包含详细内容")
        print(f"  python unified_news_crawler.py all true   # 获取全部新闻，包含详细内容")
        print(f"  python unified_news_crawler.py all false  # 获取全部新闻，不包含详细内容")
    else:
        print("爬取失败！")


if __name__ == "__main__":
    main()
