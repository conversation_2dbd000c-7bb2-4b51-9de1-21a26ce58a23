#!/usr/bin/env python3
"""
新闻爬取到向量数据库一键式主脚本
从新闻爬取 -> CSV处理 -> 向量化 -> 向量数据库 -> 搜索测试的完整流程

功能特性:
- 完整的端到端自动化流程
- 详细的步骤日志和进度显示
- 错误处理和回滚机制
- 配置验证和环境检查
- 文件管理和清理
- 性能统计和报告

使用方法:
    python master_pipeline.py [新闻数量] [是否获取详细内容] [选项]
    
示例:
    python master_pipeline.py 10 true                    # 每个来源10条，获取详细内容
    python master_pipeline.py 50 false --no-cleanup     # 每个来源50条，不获取详细内容，不清理文件
    python master_pipeline.py all true --test-search    # 全量爬取，获取详细内容，运行搜索测试
"""

import sys
import os
import time
import json
import glob
import shutil
import argparse
import subprocess
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 延迟导入，避免循环导入问题
def import_modules():
    """延迟导入模块"""
    try:
        from config import get_embedding_config, print_config_status, SystemConfig
        from csv_to_vector_processor import CSVNewsVectorProcessor
        from vector_database import SimpleVectorDB
        from news_search import NewsSearchEngine
        return get_embedding_config, print_config_status, SystemConfig, CSVNewsVectorProcessor, SimpleVectorDB, NewsSearchEngine
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("💡 请确保所有必要的文件都在当前目录")
        sys.exit(1)


class MasterPipeline:
    """新闻处理主流水线"""
    
    def __init__(self, news_count: str = "10", get_detail: str = "true",
                 cleanup: bool = True, test_search: bool = False,
                 vector_db_path: str = "news_vectors"):
        """
        初始化主流水线

        Args:
            news_count: 每个来源的新闻数量 ("10", "50", "all")
            get_detail: 是否获取详细内容 ("true", "false")
            cleanup: 是否清理中间文件
            test_search: 是否运行搜索测试
            vector_db_path: 向量数据库路径
        """
        # 导入模块
        self.get_embedding_config, self.print_config_status, self.SystemConfig, \
        self.CSVNewsVectorProcessor, self.SimpleVectorDB, self.NewsSearchEngine = import_modules()

        self.news_count = news_count
        self.get_detail = get_detail
        self.cleanup = cleanup
        self.test_search = test_search
        self.vector_db_path = vector_db_path

        # 时间戳
        self.start_time = datetime.now()
        self.timestamp = self.start_time.strftime("%Y%m%d_%H%M%S")

        # 文件路径
        self.base_dir = os.path.dirname(os.path.abspath(__file__))
        self.csv_files = []
        self.processed_files = []

        # 统计信息
        self.stats = {
            'start_time': self.start_time,
            'crawl_time': 0,
            'vector_time': 0,
            'total_time': 0,
            'news_crawled': 0,
            'news_vectorized': 0,
            'csv_files_processed': 0,
            'errors': []
        }

        # 确保必要目录存在（延迟到需要时创建）
        # self.SystemConfig.ensure_directories()
        
        print("🚀 新闻处理主流水线启动")
        print("=" * 80)
        print(f"📋 配置信息:")
        print(f"   📰 新闻数量: {self.news_count}")
        print(f"   📝 获取详情: {self.get_detail}")
        print(f"   🧹 清理文件: {self.cleanup}")
        print(f"   🔍 搜索测试: {self.test_search}")
        print(f"   💾 向量数据库: {self.vector_db_path}")
        print(f"   ⏰ 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

    def step_1_environment_check(self) -> bool:
        """步骤1: 环境检查和配置验证"""
        print("\n🔧 步骤1: 环境检查和配置验证")
        print("-" * 50)
        
        try:
            # 检查Python版本
            python_version = sys.version_info
            print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
            if python_version < (3, 8):
                raise Exception("需要Python 3.8或更高版本")
            
            # 检查必要文件
            required_files = [
                'unified_news_crawler.py',
                'csv_to_vector_processor.py', 
                'vector_database.py',
                'config.py',
                'news_search.py'
            ]
            
            missing_files = []
            for file in required_files:
                file_path = os.path.join(self.base_dir, file)
                if not os.path.exists(file_path):
                    missing_files.append(file)
                else:
                    print(f"   ✅ {file}")
            
            if missing_files:
                raise Exception(f"缺少必要文件: {missing_files}")
            
            # 检查配置
            print("\n📋 配置状态检查:")
            config_ok = self.print_config_status()
            if not config_ok:
                raise Exception("配置不完整，请检查.env文件")
            
            # 检查依赖包
            print("\n📦 检查依赖包:")
            required_packages = ['requests', 'pandas', 'numpy', 'sqlite3']
            for package in required_packages:
                try:
                    __import__(package)
                    print(f"   ✅ {package}")
                except ImportError:
                    print(f"   ❌ {package} (缺失)")
                    raise Exception(f"缺少依赖包: {package}")
            
            print("✅ 环境检查通过")
            return True
            
        except Exception as e:
            print(f"❌ 环境检查失败: {e}")
            self.stats['errors'].append(f"环境检查: {e}")
            return False

    def step_2_crawl_news(self) -> bool:
        """步骤2: 爬取新闻数据"""
        print(f"\n📰 步骤2: 爬取新闻数据")
        print("-" * 50)
        
        try:
            crawl_start = time.time()
            
            # 构建爬虫命令
            crawler_script = os.path.join(self.base_dir, 'unified_news_crawler.py')
            cmd = [sys.executable, crawler_script, self.news_count, self.get_detail]
            
            print(f"🚀 执行命令: {' '.join(cmd)}")
            print(f"📍 工作目录: {self.base_dir}")
            
            # 执行爬虫
            result = subprocess.run(
                cmd, 
                cwd=self.base_dir,
                capture_output=True, 
                text=True, 
                encoding='utf-8'
            )
            
            crawl_end = time.time()
            self.stats['crawl_time'] = crawl_end - crawl_start
            
            if result.returncode != 0:
                print(f"❌ 爬虫执行失败")
                print(f"错误输出: {result.stderr}")
                raise Exception(f"爬虫返回码: {result.returncode}")
            
            # 解析输出获取统计信息
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if '总计成功:' in line:
                    try:
                        # 提取新闻数量
                        import re
                        match = re.search(r'(\d+)\s*条新闻', line)
                        if match:
                            self.stats['news_crawled'] = int(match.group(1))
                    except:
                        pass
            
            # 查找生成的CSV文件
            csv_pattern = f"unified_news_*.csv"
            csv_files = glob.glob(os.path.join(self.base_dir, csv_pattern))
            
            # 按修改时间排序，获取最新的文件
            if csv_files:
                csv_files.sort(key=os.path.getmtime, reverse=True)
                self.csv_files = csv_files
                latest_csv = csv_files[0]
                
                print(f"✅ 新闻爬取完成")
                print(f"   📁 生成文件: {os.path.basename(latest_csv)}")
                print(f"   📊 爬取数量: {self.stats['news_crawled']} 条")
                print(f"   ⏱️  耗时: {self.stats['crawl_time']:.1f} 秒")
                
                return True
            else:
                raise Exception("未找到生成的CSV文件")
                
        except Exception as e:
            print(f"❌ 新闻爬取失败: {e}")
            self.stats['errors'].append(f"新闻爬取: {e}")
            return False

    def step_3_process_vectors(self) -> bool:
        """步骤3: 向量化处理"""
        print(f"\n🔢 步骤3: 向量化处理")
        print("-" * 50)
        
        if not self.csv_files:
            print("❌ 没有CSV文件需要处理")
            return False
        
        try:
            vector_start = time.time()
            
            # 获取配置
            embedding_config = self.get_embedding_config()

            # 初始化向量处理器
            print("🔧 初始化向量处理器...")
            processor = self.CSVNewsVectorProcessor(
                embedding_config=embedding_config,
                vector_db_path=self.vector_db_path
            )
            
            total_processed = 0
            
            # 处理每个CSV文件
            for i, csv_file in enumerate(self.csv_files, 1):
                print(f"\n📄 处理文件 {i}/{len(self.csv_files)}: {os.path.basename(csv_file)}")
                
                # 检查文件是否存在
                if not os.path.exists(csv_file):
                    print(f"⚠️ 文件不存在，跳过: {csv_file}")
                    continue
                
                # 处理CSV文件
                success = processor.process_csv_file(csv_file)
                
                if success:
                    self.processed_files.append(csv_file)
                    total_processed += 1
                    print(f"✅ 文件处理成功: {os.path.basename(csv_file)}")
                else:
                    print(f"❌ 文件处理失败: {os.path.basename(csv_file)}")
            
            vector_end = time.time()
            self.stats['vector_time'] = vector_end - vector_start
            self.stats['csv_files_processed'] = total_processed
            self.stats['news_vectorized'] = processor.stats['successful_vectorized']
            
            # 显示处理统计
            print(f"\n✅ 向量化处理完成")
            print(f"   📁 处理文件: {total_processed}/{len(self.csv_files)}")
            print(f"   📊 向量化数量: {self.stats['news_vectorized']} 条")
            print(f"   ⏱️  耗时: {self.stats['vector_time']:.1f} 秒")
            
            # 显示详细统计
            stats = processor.stats
            print(f"\n📈 详细统计:")
            print(f"   🎯 成功向量化: {stats['successful_vectorized']}")
            print(f"   ❌ 向量化失败: {stats['failed_vectorized']}")
            print(f"   🔄 跳过重复: {stats['skipped_duplicate']}")
            print(f"   📉 跳过低质量: {stats['skipped_low_quality']}")
            
            return total_processed > 0

        except Exception as e:
            print(f"❌ 向量化处理失败: {e}")
            self.stats['errors'].append(f"向量化处理: {e}")
            return False

    def step_4_verify_database(self) -> bool:
        """步骤4: 验证向量数据库"""
        print(f"\n🔍 步骤4: 验证向量数据库")
        print("-" * 50)

        try:
            # 检查数据库文件是否存在
            if not os.path.exists(self.vector_db_path):
                raise Exception(f"向量数据库不存在: {self.vector_db_path}")

            # 初始化数据库
            db = self.SimpleVectorDB(self.vector_db_path)

            # 获取统计信息
            stats = db.get_stats()

            print(f"✅ 向量数据库验证成功")
            print(f"   📊 总文档数: {stats['total_documents']}")
            print(f"   🔢 向量维度: {stats['vector_dimension']}")
            print(f"   💾 存储路径: {stats['storage_path']}")
            print(f"   📅 最后更新: {stats.get('last_updated', 'N/A')}")

            # 验证数据完整性
            if stats['total_documents'] == 0:
                raise Exception("数据库为空，没有文档")

            # 检查向量维度
            if stats['vector_dimension'] != 768:
                print(f"⚠️ 向量维度异常: {stats['vector_dimension']} (期望: 768)")

            return True

        except Exception as e:
            print(f"❌ 数据库验证失败: {e}")
            self.stats['errors'].append(f"数据库验证: {e}")
            return False

    def step_5_test_search(self) -> bool:
        """步骤5: 搜索功能测试"""
        if not self.test_search:
            print(f"\n🔍 步骤5: 搜索功能测试 (跳过)")
            print("-" * 50)
            print("⏭️ 跳过搜索测试")
            return True

        print(f"\n🔍 步骤5: 搜索功能测试")
        print("-" * 50)

        try:
            # 初始化搜索引擎
            search_engine = self.NewsSearchEngine(self.vector_db_path)

            # 测试查询列表
            test_queries = [
                "科技新闻",
                "经济发展",
                "国际关系",
                "社会热点"
            ]

            print("🧪 执行搜索测试:")

            for i, query in enumerate(test_queries, 1):
                print(f"\n   🔍 测试查询 {i}: '{query}'")

                try:
                    results = search_engine.search(query, top_k=3)

                    if results:
                        print(f"      ✅ 找到 {len(results)} 条结果")
                        for j, result in enumerate(results[:2], 1):  # 只显示前2条
                            similarity = result.get('similarity', 0)
                            title = result.get('title', 'N/A')[:50]
                            print(f"         {j}. {title}... (相似度: {similarity:.3f})")
                    else:
                        print(f"      ⚠️ 未找到相关结果")

                except Exception as e:
                    print(f"      ❌ 查询失败: {e}")

            print(f"\n✅ 搜索功能测试完成")
            return True

        except Exception as e:
            print(f"❌ 搜索测试失败: {e}")
            self.stats['errors'].append(f"搜索测试: {e}")
            return False

    def step_6_cleanup_files(self) -> bool:
        """步骤6: 清理中间文件"""
        if not self.cleanup:
            print(f"\n🧹 步骤6: 清理中间文件 (跳过)")
            print("-" * 50)
            print("⏭️ 跳过文件清理")
            return True

        print(f"\n🧹 步骤6: 清理中间文件")
        print("-" * 50)

        try:
            cleaned_files = 0

            # 清理已处理的CSV文件
            for csv_file in self.processed_files:
                if os.path.exists(csv_file):
                    try:
                        # 移动到归档目录而不是删除
                        archive_dir = self.SystemConfig.ARCHIVE_DIR
                        os.makedirs(archive_dir, exist_ok=True)

                        filename = os.path.basename(csv_file)
                        archive_path = os.path.join(archive_dir, f"{self.timestamp}_{filename}")

                        shutil.move(csv_file, archive_path)
                        print(f"   📦 归档: {filename}")
                        cleaned_files += 1

                    except Exception as e:
                        print(f"   ❌ 归档失败 {os.path.basename(csv_file)}: {e}")

            # 清理临时文件
            temp_patterns = ['*.tmp', '*.temp', '__pycache__']
            for pattern in temp_patterns:
                temp_files = glob.glob(os.path.join(self.base_dir, pattern))
                for temp_file in temp_files:
                    try:
                        if os.path.isfile(temp_file):
                            os.remove(temp_file)
                        elif os.path.isdir(temp_file):
                            shutil.rmtree(temp_file)
                        print(f"   🗑️ 删除: {os.path.basename(temp_file)}")
                        cleaned_files += 1
                    except Exception as e:
                        print(f"   ❌ 删除失败 {temp_file}: {e}")

            print(f"✅ 文件清理完成")
            print(f"   📁 处理文件: {cleaned_files} 个")

            return True

        except Exception as e:
            print(f"❌ 文件清理失败: {e}")
            self.stats['errors'].append(f"文件清理: {e}")
            return False

    def generate_report(self) -> Dict:
        """生成执行报告"""
        end_time = datetime.now()
        self.stats['end_time'] = end_time
        self.stats['total_time'] = (end_time - self.start_time).total_seconds()

        return {
            'timestamp': self.timestamp,
            'start_time': self.start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'total_time_seconds': self.stats['total_time'],
            'crawl_time_seconds': self.stats['crawl_time'],
            'vector_time_seconds': self.stats['vector_time'],
            'news_crawled': self.stats['news_crawled'],
            'news_vectorized': self.stats['news_vectorized'],
            'csv_files_processed': self.stats['csv_files_processed'],
            'errors': self.stats['errors'],
            'success': len(self.stats['errors']) == 0,
            'config': {
                'news_count': self.news_count,
                'get_detail': self.get_detail,
                'cleanup': self.cleanup,
                'test_search': self.test_search,
                'vector_db_path': self.vector_db_path
            }
        }

    def save_report(self, report: Dict) -> str:
        """保存执行报告"""
        try:
            # 导入模块（如果还没有导入）
            if not hasattr(self, 'SystemConfig'):
                self.get_embedding_config, self.print_config_status, self.SystemConfig, \
                self.CSVNewsVectorProcessor, self.SimpleVectorDB, self.NewsSearchEngine = import_modules()

            report_dir = self.SystemConfig.LOG_DIR
            os.makedirs(report_dir, exist_ok=True)

            report_file = os.path.join(report_dir, f"pipeline_report_{self.timestamp}.json")

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            return report_file

        except Exception as e:
            print(f"⚠️ 保存报告失败: {e}")
            return ""

    def print_final_report(self, report: Dict):
        """打印最终报告"""
        print("\n" + "=" * 80)
        print("📊 执行报告")
        print("=" * 80)

        # 基本信息
        print(f"🕐 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🕐 结束时间: {report['end_time'][:19].replace('T', ' ')}")
        print(f"⏱️  总耗时: {report['total_time_seconds']:.1f} 秒")

        # 性能统计
        print(f"\n📈 性能统计:")
        print(f"   🕷️  爬取耗时: {report['crawl_time_seconds']:.1f} 秒")
        print(f"   🔢 向量化耗时: {report['vector_time_seconds']:.1f} 秒")
        if report['crawl_time_seconds'] > 0:
            crawl_speed = report['news_crawled'] / report['crawl_time_seconds']
            print(f"   📊 爬取速度: {crawl_speed:.2f} 条/秒")
        if report['vector_time_seconds'] > 0:
            vector_speed = report['news_vectorized'] / report['vector_time_seconds']
            print(f"   📊 向量化速度: {vector_speed:.2f} 条/秒")

        # 数据统计
        print(f"\n📊 数据统计:")
        print(f"   📰 爬取新闻: {report['news_crawled']} 条")
        print(f"   🔢 向量化新闻: {report['news_vectorized']} 条")
        print(f"   📁 处理文件: {report['csv_files_processed']} 个")

        # 成功率
        if report['news_crawled'] > 0:
            success_rate = (report['news_vectorized'] / report['news_crawled']) * 100
            print(f"   ✅ 成功率: {success_rate:.1f}%")

        # 错误信息
        if report['errors']:
            print(f"\n❌ 错误信息 ({len(report['errors'])} 个):")
            for i, error in enumerate(report['errors'], 1):
                print(f"   {i}. {error}")

        # 最终状态
        print(f"\n🎯 最终状态: {'✅ 成功' if report['success'] else '❌ 失败'}")

        print("=" * 80)

    def run(self) -> bool:
        """运行完整流水线"""
        print("🚀 开始执行完整流水线")

        steps = [
            ("环境检查", self.step_1_environment_check),
            ("爬取新闻", self.step_2_crawl_news),
            ("向量化处理", self.step_3_process_vectors),
            ("验证数据库", self.step_4_verify_database),
            ("搜索测试", self.step_5_test_search),
            ("清理文件", self.step_6_cleanup_files)
        ]

        success_count = 0

        for i, (step_name, step_func) in enumerate(steps, 1):
            print(f"\n{'='*20} 步骤 {i}/{len(steps)}: {step_name} {'='*20}")

            try:
                success = step_func()
                if success:
                    success_count += 1
                    print(f"✅ 步骤 {i} 完成: {step_name}")
                else:
                    print(f"❌ 步骤 {i} 失败: {step_name}")
                    # 某些步骤失败后可以继续，某些步骤失败后必须停止
                    if step_name in ["环境检查", "爬取新闻", "向量化处理"]:
                        print(f"💥 关键步骤失败，停止执行")
                        break

            except Exception as e:
                print(f"💥 步骤 {i} 异常: {step_name} - {e}")
                self.stats['errors'].append(f"{step_name}: {e}")
                if step_name in ["环境检查", "爬取新闻", "向量化处理"]:
                    break

        # 生成和保存报告
        report = self.generate_report()
        report_file = self.save_report(report)

        # 打印最终报告
        self.print_final_report(report)

        if report_file:
            print(f"📄 详细报告已保存: {report_file}")

        # 返回是否成功
        pipeline_success = success_count >= 4  # 至少前4个步骤成功

        if pipeline_success:
            print(f"\n🎉 流水线执行成功！")
            print(f"💡 现在可以使用以下命令进行新闻搜索:")
            print(f"   python news_search.py")
        else:
            print(f"\n💥 流水线执行失败，请检查错误信息")

        return pipeline_success


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="新闻爬取到向量数据库一键式主脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s 10 true                    # 每个来源10条，获取详细内容
  %(prog)s 50 false --no-cleanup     # 每个来源50条，不获取详细内容，不清理文件
  %(prog)s all true --test-search    # 全量爬取，获取详细内容，运行搜索测试
  %(prog)s 20 true --db-path my_db   # 自定义数据库路径
        """
    )

    # 位置参数
    parser.add_argument(
        'news_count',
        nargs='?',
        default='10',
        help='每个来源的新闻数量 (数字或"all"，默认: 10)'
    )

    parser.add_argument(
        'get_detail',
        nargs='?',
        default='true',
        choices=['true', 'false'],
        help='是否获取详细内容 (true/false，默认: true)'
    )

    # 可选参数
    parser.add_argument(
        '--no-cleanup',
        action='store_true',
        help='不清理中间文件'
    )

    parser.add_argument(
        '--test-search',
        action='store_true',
        help='运行搜索功能测试'
    )

    parser.add_argument(
        '--db-path',
        default='news_vectors',
        help='向量数据库路径 (默认: news_vectors)'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细输出'
    )

    parser.add_argument(
        '--version',
        action='version',
        version='%(prog)s 1.0.0'
    )

    return parser.parse_args()


def validate_arguments(args):
    """验证命令行参数"""
    errors = []

    # 验证新闻数量
    if args.news_count != 'all':
        try:
            count = int(args.news_count)
            if count <= 0:
                errors.append("新闻数量必须大于0")
            elif count > 1000:
                errors.append("新闻数量不建议超过1000")
        except ValueError:
            errors.append(f"无效的新闻数量: {args.news_count}")

    # 验证数据库路径
    if not args.db_path or args.db_path.strip() == '':
        errors.append("数据库路径不能为空")

    return errors


def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()

        # 验证参数
        validation_errors = validate_arguments(args)
        if validation_errors:
            print("❌ 参数验证失败:")
            for error in validation_errors:
                print(f"   • {error}")
            sys.exit(1)

        # 显示启动信息
        print("🚀 新闻处理主流水线")
        print("=" * 60)
        print(f"版本: 1.0.0")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Python: {sys.version.split()[0]}")
        print("=" * 60)

        # 创建并运行流水线
        pipeline = MasterPipeline(
            news_count=args.news_count,
            get_detail=args.get_detail,
            cleanup=not args.no_cleanup,
            test_search=args.test_search,
            vector_db_path=args.db_path
        )

        # 执行流水线
        success = pipeline.run()

        # 退出码
        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断执行")
        sys.exit(130)

    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
