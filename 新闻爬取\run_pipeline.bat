@echo off
chcp 65001 >nul
title 新闻处理主流水线

echo.
echo ========================================
echo    新闻处理主流水线 - 快速启动
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 显示菜单
:menu
echo 请选择运行模式:
echo.
echo 1. 快速测试 (每个来源5条新闻)
echo 2. 标准处理 (每个来源10条新闻)
echo 3. 大量处理 (每个来源50条新闻)
echo 4. 全量处理 (所有可用新闻)
echo 5. 自定义参数
echo 6. 查看帮助
echo 0. 退出
echo.

set /p choice="请输入选择 (0-6): "

if "%choice%"=="1" goto quick_test
if "%choice%"=="2" goto standard
if "%choice%"=="3" goto large
if "%choice%"=="4" goto full
if "%choice%"=="5" goto custom
if "%choice%"=="6" goto help
if "%choice%"=="0" goto exit
goto invalid

:quick_test
echo.
echo 🚀 执行快速测试...
python master_pipeline.py 5 true --test-search
goto end

:standard
echo.
echo 🚀 执行标准处理...
python master_pipeline.py 10 true
goto end

:large
echo.
echo 🚀 执行大量处理...
python master_pipeline.py 50 true
goto end

:full
echo.
echo 🚀 执行全量处理...
python master_pipeline.py all true --test-search
goto end

:custom
echo.
echo 自定义参数设置:
echo.
set /p news_count="新闻数量 (数字或all, 默认10): "
if "%news_count%"=="" set news_count=10

set /p get_detail="获取详情 (true/false, 默认true): "
if "%get_detail%"=="" set get_detail=true

set /p test_search="搜索测试 (y/n, 默认n): "
set test_search_param=
if /i "%test_search%"=="y" set test_search_param=--test-search

set /p no_cleanup="保留文件 (y/n, 默认n): "
set cleanup_param=
if /i "%no_cleanup%"=="y" set cleanup_param=--no-cleanup

echo.
echo 🚀 执行自定义处理...
python master_pipeline.py %news_count% %get_detail% %test_search_param% %cleanup_param%
goto end

:help
echo.
echo 📖 使用帮助:
echo.
python master_pipeline.py --help
echo.
pause
goto menu

:invalid
echo.
echo ❌ 无效选择，请重新输入
echo.
goto menu

:end
echo.
if errorlevel 1 (
    echo ❌ 执行失败，请检查错误信息
) else (
    echo ✅ 执行完成
    echo.
    echo 💡 现在可以使用搜索功能:
    echo    python news_search.py
)
echo.
pause

:exit
echo.
echo 👋 再见！
timeout /t 2 >nul
