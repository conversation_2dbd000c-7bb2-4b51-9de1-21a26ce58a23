# 界面新闻统一爬虫

这是一个功能完整的界面新闻网站统一爬虫，能够自动爬取**财经、新闻、商业**三大主分类下所有子分类的当天新闻并提取完整的文章内容。

## 🚀 功能特点

- **全面覆盖**：支持爬取界面新闻网站的78个分类的新闻（财经31个 + 新闻15个 + 商业32个）
- **三大分类**：财经分类、新闻分类、商业分类一网打尽
- **智能筛选**：自动筛选当天发布的新闻文章
- **完整内容**：不仅提取基础信息，还能获取文章完整正文、图片等详细内容
- **多格式输出**：支持CSV和JSON两种输出格式
- **稳定可靠**：包含完善的错误处理和重试机制
- **详细日志**：提供详细的运行日志和进度显示
- **一键运行**：无需交互，直接运行即可获取所有数据

## 支持的新闻分类

### 财经分类（31个）
科技、地产、汽车、消费、工业、时尚、交通、医药、互联网、创投、能源、数码、教育、食品、新能源、家电、健康、酒业、物流、零售、美妆、体育、家居、餐饮、日用、企服、珠宝、腕表、智库、电厂、农业

### 新闻分类（15个）
天下、中国、地方、评论、数据、职场、国是、文娱、影像、营销、大湾区、ESG、双碳、长三角、中西部

### 商业分类（32个）
科技、地产、汽车、消费、工业、时尚、交通、医药、互联网、创投、能源、数码、教育、食品、新能源、家电、健康、酒业、物流、零售、美妆、体育、楼市、家居、餐饮、日用、企服、珠宝、腕表、智库、电厂、农业

## 安装依赖

```bash
pip install -r requirements.txt
```

## 📖 使用方法

### 快速开始

直接运行统一爬虫脚本即可开始爬取：

```bash
python jiemian_unified_crawler.py
```

脚本会自动：
1. 爬取财经分类（31个）的当天新闻
2. 爬取新闻分类（15个）的当天新闻
3. 爬取商业分类（32个）的当天新闻
4. 逐一获取每篇文章的详细内容（正文、图片等）
5. 生成3个输出文件

### 程序化使用

```python
from jiemian_unified_crawler import JiemianUnifiedCrawler

# 创建统一爬虫实例
crawler = JiemianUnifiedCrawler()

# 爬取所有分类的当天新闻（包含详细内容）
detailed_articles = crawler.crawl_all_categories()

# 或者爬取特定分类
tech_articles = crawler.crawl_category_news(65, "科技", "finance")

# 爬取单篇文章的详细内容
article_detail = crawler.crawl_article_detail("https://www.jiemian.com/article/12345678.html")
```

## 📁 输出文件

运行后会生成以下文件：

- `jiemian_unified_YYYY-MM-DD.csv` - CSV格式的基础新闻数据
- `jiemian_unified_YYYY-MM-DD.json` - JSON格式的基础新闻数据
- `jiemian_unified_YYYY-MM-DD_detailed.json` - 包含完整内容的详细数据
- `jiemian_unified_crawler.log` - 爬虫运行日志

## 📊 数据字段说明

### 基础信息字段

每篇文章的基础信息包含：

- `category_type` - 分类类型（finance/news/business）
- `category` - 新闻分类名称
- `category_id` - 分类ID
- `title` - 文章标题
- `link` - 文章链接
- `summary` - 文章摘要
- `author` - 作者
- `publish_time` - 发布时间
- `views` - 阅读量

### 详细内容字段

详细数据文件还包含：

- `url` - 文章完整URL
- `content` - 文章完整正文内容
- `source` - 新闻来源
- `images` - 文章中的图片信息列表
  - `src` - 图片URL
  - `alt` - 图片描述

## ⚡ 运行示例

```bash
$ python jiemian_unified_crawler.py

界面新闻统一爬虫 - 完整内容爬取
============================================================
目标日期: 2025-07-07
财经分类数量: 31
新闻分类数量: 15
商业分类数量: 32
总分类数量: 78
正在爬取所有分类的完整内容（财经+新闻+商业）...
注意：爬取详细内容需要更多时间，请耐心等待...

正在爬取财经分类...
2025-07-07 13:51:19,636 - INFO - 开始爬取 科技 分类新闻
2025-07-07 13:51:23,241 - INFO - 科技 分类找到 5 篇当天文章
正在爬取新闻分类...
正在爬取商业分类...
...

开始爬取文章详细内容...
2025-07-07 13:52:26,390 - INFO - 开始爬取 45 篇文章的详细内容
...

爬取完成！
总共获取 45 篇文章的完整内容

财经分类文章数量 (共15篇):
  科技: 5 篇
  数码: 3 篇
  汽车: 2 篇
  交通: 2 篇
  消费: 1 篇
  酒业: 1 篇
  零售: 1 篇

新闻分类文章数量 (共18篇):
  天下: 8 篇
  中国: 6 篇
  地方: 4 篇

商业分类文章数量 (共12篇):
  科技: 4 篇
  汽车: 3 篇
  消费: 2 篇
  地产: 2 篇
  健康: 1 篇

输出文件:
  - jiemian_unified_2025-07-07.csv (基础信息)
  - jiemian_unified_2025-07-07.json (基础信息)
  - jiemian_unified_2025-07-07_detailed.json (详细内容)
```

## ⚠️ 注意事项

1. **合规使用**：请遵守网站的robots.txt协议和使用条款
2. **请求频率**：脚本已内置合理的请求间隔，避免对服务器造成压力
3. **网络环境**：如遇到网络问题，脚本会自动重试
4. **数据时效**：仅爬取当天发布的新闻文章
5. **结构变化**：如网站结构发生变化，可能需要更新解析逻辑

## 🛠️ 技术实现

- **HTTP请求**：使用 `requests` 库发送HTTP请求，支持会话保持
- **HTML解析**：使用 `BeautifulSoup` 解析HTML内容，支持多种选择器
- **内容提取**：智能识别文章结构，提取标题、正文、图片等信息
- **时间匹配**：使用正则表达式精确匹配各种时间格式
- **错误处理**：完善的异常处理和重试机制
- **数据输出**：支持CSV和JSON多种格式输出
- **日志记录**：详细的运行日志，便于调试和监控

## 📋 依赖要求

- Python 3.6+
- requests >= 2.25.1
- beautifulsoup4 >= 4.9.3
- lxml >= 4.6.3

## 📄 许可证

本项目仅供学习和研究使用，请勿用于商业用途。使用时请遵守相关法律法规和网站使用条款。
