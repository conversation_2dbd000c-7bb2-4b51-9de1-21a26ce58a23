#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻向量数据库 Web API
提供RESTful API接口访问新闻数据
"""

from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import os
import json
from datetime import datetime
from typing import List, Dict
from vector_database import VectorDatabase
from config import SystemConfig

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 全局变量
vector_db = None
config = None

def init_database():
    """初始化数据库"""
    global vector_db, config
    config = SystemConfig()
    vector_db = VectorDatabase(config.VECTOR_DB_PATH)
    print(f"✅ API服务器初始化完成")
    print(f"   数据库路径: {config.VECTOR_DB_PATH}")
    print(f"   总新闻数: {len(vector_db.doc_ids)}")

@app.route('/')
def index():
    """首页 - 简单的Web界面"""
    html_template = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>新闻向量数据库 API</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; text-align: center; }
            .api-section { margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 5px; }
            .endpoint { background: #e9ecef; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace; }
            .search-box { margin: 20px 0; }
            input[type="text"] { width: 70%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
            button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background: #0056b3; }
            .results { margin-top: 20px; }
            .news-item { background: white; margin: 10px 0; padding: 15px; border-left: 4px solid #007bff; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>📰 新闻向量数据库 API</h1>
            
            <div class="api-section">
                <h3>📊 数据库状态</h3>
                <p>总新闻数: <strong>{{ total_docs }}</strong></p>
                <p>向量维度: <strong>{{ vector_dim }}</strong></p>
                <p>数据库路径: <code>{{ db_path }}</code></p>
            </div>
            
            <div class="api-section">
                <h3>🔍 新闻搜索</h3>
                <div class="search-box">
                    <input type="text" id="searchQuery" placeholder="输入搜索关键词..." />
                    <button onclick="searchNews()">搜索</button>
                </div>
                <div id="searchResults" class="results"></div>
            </div>
            
            <div class="api-section">
                <h3>🛠️ API 接口</h3>
                <div class="endpoint">GET /api/stats - 获取数据库统计信息</div>
                <div class="endpoint">GET /api/search?q=关键词&top_k=5&threshold=0.3 - 搜索新闻</div>
                <div class="endpoint">GET /api/categories - 获取所有分类</div>
                <div class="endpoint">GET /api/category/分类名 - 获取指定分类的新闻</div>
                <div class="endpoint">GET /api/news/文档ID - 获取指定新闻详情</div>
            </div>
        </div>
        
        <script>
            function searchNews() {
                const query = document.getElementById('searchQuery').value;
                if (!query.trim()) {
                    alert('请输入搜索关键词');
                    return;
                }
                
                fetch(`/api/search?q=${encodeURIComponent(query)}&top_k=5`)
                    .then(response => response.json())
                    .then(data => {
                        const resultsDiv = document.getElementById('searchResults');
                        if (data.success && data.results.length > 0) {
                            let html = '<h4>搜索结果:</h4>';
                            data.results.forEach((item, index) => {
                                const metadata = item.metadata;
                                html += `
                                    <div class="news-item">
                                        <h5>${metadata.title || '无标题'}</h5>
                                        <p><strong>相似度:</strong> ${item.similarity.toFixed(3)}</p>
                                        <p><strong>分类:</strong> ${metadata.category || '未知'} | <strong>来源:</strong> ${metadata.media || '未知'}</p>
                                        <p><strong>时间:</strong> ${metadata.formatted_time || '未知'}</p>
                                        <p><strong>链接:</strong> <a href="${metadata.url}" target="_blank">${metadata.url}</a></p>
                                    </div>
                                `;
                            });
                            resultsDiv.innerHTML = html;
                        } else {
                            resultsDiv.innerHTML = '<p>没有找到相关新闻</p>';
                        }
                    })
                    .catch(error => {
                        console.error('搜索出错:', error);
                        document.getElementById('searchResults').innerHTML = '<p>搜索出错，请稍后重试</p>';
                    });
            }
            
            // 回车键搜索
            document.getElementById('searchQuery').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchNews();
                }
            });
        </script>
    </body>
    </html>
    """
    
    return render_template_string(html_template, 
                                total_docs=len(vector_db.doc_ids),
                                vector_dim=vector_db.vector_dim,
                                db_path=config.VECTOR_DB_PATH)

@app.route('/api/stats')
def get_stats():
    """获取数据库统计信息"""
    try:
        stats = vector_db.get_stats()
        return jsonify({
            'success': True,
            'stats': stats
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/search')
def search_news():
    """搜索新闻"""
    try:
        query = request.args.get('q', '').strip()
        top_k = int(request.args.get('top_k', 5))
        threshold = float(request.args.get('threshold', 0.3))
        
        if not query:
            return jsonify({
                'success': False,
                'error': '搜索查询不能为空'
            }), 400
        
        results = vector_db.search(query, top_k=top_k, threshold=threshold)
        
        return jsonify({
            'success': True,
            'query': query,
            'total_results': len(results),
            'results': results
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/categories')
def get_categories():
    """获取所有分类"""
    try:
        stats = vector_db.get_stats()
        categories = stats.get('categories', {})
        
        return jsonify({
            'success': True,
            'categories': categories
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/category/<category_name>')
def get_news_by_category(category_name):
    """获取指定分类的新闻"""
    try:
        limit = int(request.args.get('limit', 20))
        
        matching_news = []
        for doc_id in vector_db.doc_ids:
            metadata = vector_db.metadata.get(doc_id, {})
            if metadata.get('category', '') == category_name:
                matching_news.append({
                    'doc_id': doc_id,
                    'metadata': metadata
                })
                
                if len(matching_news) >= limit:
                    break
        
        return jsonify({
            'success': True,
            'category': category_name,
            'total_results': len(matching_news),
            'results': matching_news
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/news/<doc_id>')
def get_news_detail(doc_id):
    """获取指定新闻详情"""
    try:
        if doc_id not in vector_db.doc_ids:
            return jsonify({
                'success': False,
                'error': '新闻不存在'
            }), 404
        
        metadata = vector_db.metadata.get(doc_id, {})
        
        # 查找相似新闻
        similar_news = vector_db.find_similar_documents(doc_id, top_k=5, threshold=0.5)
        
        return jsonify({
            'success': True,
            'doc_id': doc_id,
            'metadata': metadata,
            'similar_news': similar_news
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': '接口不存在'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': '服务器内部错误'
    }), 500

def main():
    """启动API服务器"""
    print("🚀 启动新闻向量数据库 API 服务器")
    print("=" * 50)
    
    # 初始化数据库
    init_database()
    
    print("\n📡 API 服务器信息:")
    print("   地址: http://localhost:5000")
    print("   Web界面: http://localhost:5000")
    print("   API文档: http://localhost:5000/api/stats")
    print("\n🔗 主要API接口:")
    print("   GET /api/search?q=关键词 - 搜索新闻")
    print("   GET /api/stats - 数据库统计")
    print("   GET /api/categories - 获取分类")
    print("   GET /api/category/分类名 - 分类新闻")
    print("   GET /api/news/文档ID - 新闻详情")
    print("\n按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    # 启动服务器
    app.run(host='0.0.0.0', port=5000, debug=False)

if __name__ == "__main__":
    main()
