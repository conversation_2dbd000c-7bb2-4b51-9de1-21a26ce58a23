"""
自动化新闻处理脚本
支持定时处理、手动处理、监控模式等
"""

import os
import sys
import time
import argparse
import schedule
import signal
from datetime import datetime, timedelta
from typing import Optional
import logging

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from unified_news_processor import UnifiedNewsProcessor
    from config import get_system_config
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("💡 请确保相关文件在当前目录")
    sys.exit(1)


class AutoNewsProcessor:
    """自动化新闻处理器"""
    
    def __init__(self):
        self.system_config = get_system_config()
        self.processor = None
        self.running = False
        self.last_run_time = None
        
        # 设置日志
        self._setup_logging()
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _setup_logging(self):
        """设置日志"""
        log_file = self.system_config.get_log_file_path("auto_processor")
        
        logging.basicConfig(
            level=getattr(logging, self.system_config.LOG_LEVEL),
            format=self.system_config.LOG_FORMAT,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"📡 接收到信号 {signum}，准备退出...")
        self.running = False
    
    def run_processing(self) -> bool:
        """执行一次处理"""
        try:
            self.logger.info("🚀 开始自动新闻处理")
            self.logger.info("=" * 60)
            
            # 创建处理器
            if not self.processor:
                self.processor = UnifiedNewsProcessor()
            
            # 处理所有CSV文件
            stats = self.processor.process_all_csv_files()
            
            # 记录处理结果
            self.last_run_time = datetime.now()
            
            # 检查是否有新数据
            if stats['files_processed'] > 0:
                self.logger.info(f"✅ 处理完成: 处理了 {stats['files_processed']} 个文件")
                self.logger.info(f"   成功向量化: {stats['successful_vectorized']}")
                self.logger.info(f"   跳过重复: {stats['skipped_duplicates']}")
                return True
            else:
                self.logger.info("📭 没有新文件需要处理")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 自动处理失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False
    
    def run_scheduled(self, interval_minutes: int = 30):
        """定时运行模式"""
        self.logger.info(f"⏰ 启动定时处理模式，间隔: {interval_minutes} 分钟")
        
        # 设置定时任务
        schedule.every(interval_minutes).minutes.do(self.run_processing)
        
        # 立即执行一次
        self.run_processing()
        
        self.running = True
        
        try:
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            self.logger.info("⚠️ 用户中断定时处理")
        finally:
            self.running = False
            self.logger.info("🛑 定时处理已停止")
    
    def run_monitor(self, check_interval: int = 300):
        """监控模式 - 检测到新文件时自动处理"""
        self.logger.info(f"👁️ 启动监控模式，检查间隔: {check_interval} 秒")
        
        self.running = True
        last_check_time = datetime.now()
        
        try:
            while self.running:
                # 检查是否有新文件
                csv_pattern = self.system_config.get_csv_pattern()
                import glob
                csv_files = glob.glob(csv_pattern)
                
                # 检查是否有新文件（修改时间晚于上次检查）
                new_files = []
                for csv_file in csv_files:
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(csv_file))
                    if file_mtime > last_check_time:
                        new_files.append(csv_file)
                
                if new_files:
                    self.logger.info(f"🔍 发现 {len(new_files)} 个新文件，开始处理...")
                    self.run_processing()
                
                last_check_time = datetime.now()
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            self.logger.info("⚠️ 用户中断监控模式")
        finally:
            self.running = False
            self.logger.info("🛑 监控模式已停止")
    
    def run_once(self):
        """单次运行模式"""
        self.logger.info("🎯 单次处理模式")
        return self.run_processing()
    
    def show_status(self):
        """显示状态信息"""
        print("📊 自动新闻处理器状态")
        print("=" * 40)
        
        # 系统配置
        print(f"📁 CSV目录: {self.system_config.CSV_DIR}")
        print(f"🗄️ 向量数据库: {self.system_config.VECTOR_DB_PATH}")
        print(f"📦 归档目录: {self.system_config.ARCHIVE_DIR}")
        print(f"🔄 自动清理: {self.system_config.AUTO_CLEANUP_CSV}")
        print(f"🔍 重复检测: {self.system_config.DUPLICATE_CHECK_ENABLED}")
        
        # 运行状态
        print(f"\n⏱️ 运行状态:")
        print(f"   当前状态: {'运行中' if self.running else '停止'}")
        if self.last_run_time:
            print(f"   上次运行: {self.last_run_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 文件状态
        csv_pattern = self.system_config.get_csv_pattern()
        import glob
        csv_files = glob.glob(csv_pattern)
        print(f"   待处理文件: {len(csv_files)}")
        
        # 向量数据库状态
        if os.path.exists(f"{self.system_config.VECTOR_DB_PATH}.db"):
            try:
                from vector_database import VectorDatabase
                db = VectorDatabase(self.system_config.VECTOR_DB_PATH)
                db.load_database()
                stats = db.get_stats()
                print(f"   数据库文档数: {stats.get('total_documents', 0)}")
                print(f"   数据库大小: {self._get_db_size():.1f} MB")
            except Exception as e:
                print(f"   数据库状态: 无法读取 ({e})")
        else:
            print(f"   数据库状态: 不存在")
    
    def _get_db_size(self) -> float:
        """获取数据库大小（MB）"""
        try:
            db_file = f"{self.system_config.VECTOR_DB_PATH}.db"
            if os.path.exists(db_file):
                size_bytes = os.path.getsize(db_file)
                return size_bytes / (1024 * 1024)
            return 0.0
        except:
            return 0.0


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="自动化新闻处理脚本")
    parser.add_argument("--mode", choices=["once", "scheduled", "monitor", "status"], 
                       default="once", help="运行模式")
    parser.add_argument("--interval", type=int, default=30, 
                       help="定时模式的间隔时间（分钟）")
    parser.add_argument("--check-interval", type=int, default=300, 
                       help="监控模式的检查间隔（秒）")
    
    args = parser.parse_args()
    
    # 创建自动处理器
    auto_processor = AutoNewsProcessor()
    
    if args.mode == "once":
        print("🎯 单次处理模式")
        success = auto_processor.run_once()
        sys.exit(0 if success else 1)
        
    elif args.mode == "scheduled":
        print(f"⏰ 定时处理模式 (间隔: {args.interval} 分钟)")
        auto_processor.run_scheduled(args.interval)
        
    elif args.mode == "monitor":
        print(f"👁️ 监控模式 (检查间隔: {args.check_interval} 秒)")
        auto_processor.run_monitor(args.check_interval)
        
    elif args.mode == "status":
        auto_processor.show_status()


if __name__ == "__main__":
    main()
