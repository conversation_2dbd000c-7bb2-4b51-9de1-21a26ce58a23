"""
测试增量向量数据库处理
验证新数据能够正确追加到现有向量数据库中
"""

import pandas as pd
import os
import sys
from datetime import datetime
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from csv_to_vector_processor import CSVNewsVectorProcessor
    from config import get_embedding_config
    from vector_database import SimpleVectorDB
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def create_test_csv_data():
    """创建测试用的新闻数据"""
    test_news = [
        {
            'id': 9001,
            'title': '测试新闻：人工智能技术发展迅速',
            'url': 'https://test.com/ai-news-1',
            'media': '测试媒体',
            'create_date': '2025-07-07',
            'create_time': '15:00:00',
            'formatted_time': '07-07 15:00',
            'category': '科技',
            'ranking_type': '科技排行',
            'rank': 1,
            'comment_url': '',
            'top_num': '',
            'crawl_time': '2025-07-07 15:00:00',
            'content': '人工智能技术在各个领域都取得了重大突破。机器学习、深度学习、自然语言处理等技术不断发展，为社会带来了巨大的变革。专家预测，未来十年人工智能将在医疗、教育、交通等领域发挥更大作用。'
        },
        {
            'id': 9002,
            'title': '测试新闻：新能源汽车销量创新高',
            'url': 'https://test.com/ev-news-1',
            'media': '测试媒体',
            'create_date': '2025-07-07',
            'create_time': '15:30:00',
            'formatted_time': '07-07 15:30',
            'category': '汽车',
            'ranking_type': '汽车排行',
            'rank': 1,
            'comment_url': '',
            'top_num': '',
            'crawl_time': '2025-07-07 15:30:00',
            'content': '今年上半年，新能源汽车销量同比增长超过50%，创历史新高。电动汽车技术不断成熟，充电基础设施日益完善，消费者对新能源汽车的接受度持续提升。业内人士认为，新能源汽车将成为未来汽车市场的主流。'
        },
        {
            'id': 9003,
            'title': '测试新闻：量子计算研究获得重大进展',
            'url': 'https://test.com/quantum-news-1',
            'media': '测试媒体',
            'create_date': '2025-07-07',
            'create_time': '16:00:00',
            'formatted_time': '07-07 16:00',
            'category': '科技',
            'ranking_type': '科技排行',
            'rank': 2,
            'comment_url': '',
            'top_num': '',
            'crawl_time': '2025-07-07 16:00:00',
            'content': '科学家在量子计算领域取得重大突破，成功实现了100量子比特的稳定操控。这一成果将大大推进量子计算的实用化进程。量子计算在密码学、药物发现、金融建模等领域具有巨大潜力，有望解决传统计算机无法处理的复杂问题。'
        }
    ]
    
    return test_news


def save_test_csv(test_data, filename="test_news.csv"):
    """保存测试数据为CSV文件"""
    df = pd.DataFrame(test_data)
    df.to_csv(filename, index=False, encoding='utf-8')
    print(f"✅ 测试CSV文件已保存: {filename}")
    return filename


def test_incremental_processing():
    """测试增量处理功能"""
    print("🚀 开始测试增量向量数据库处理")
    print("="*60)
    
    # 获取配置
    config = get_embedding_config()
    
    # 检查现有数据库状态
    print("📊 检查现有向量数据库状态...")
    if os.path.exists("news_vectors"):
        db = SimpleVectorDB("news_vectors")
        stats = db.get_stats()
        print(f"   现有文档数: {stats['total_documents']}")
        print(f"   向量维度: {stats['vector_dimension']}")
        original_count = stats['total_documents']
    else:
        print("   未找到现有数据库，将创建新数据库")
        original_count = 0
    
    # 创建测试数据
    print("\n📝 创建测试新闻数据...")
    test_data = create_test_csv_data()
    test_csv_file = save_test_csv(test_data)
    
    # 创建处理器并处理新数据
    print("\n🔄 开始处理新数据...")
    processor = CSVNewsVectorProcessor(config, "news_vectors")
    
    # 处理测试CSV文件
    processor.process_csv_file(
        csv_file_path=test_csv_file,
        batch_size=5,
        max_news=None
    )
    
    # 检查处理后的数据库状态
    print("\n📊 检查处理后的数据库状态...")
    final_stats = processor.vector_db.get_stats()
    final_count = final_stats['total_documents']
    
    print(f"   处理前文档数: {original_count}")
    print(f"   处理后文档数: {final_count}")
    print(f"   新增文档数: {final_count - original_count}")
    
    # 测试搜索新添加的内容
    print("\n🔍 测试搜索新添加的内容...")
    test_queries = [
        "人工智能技术发展",
        "新能源汽车销量",
        "量子计算研究"
    ]
    
    for query in test_queries:
        print(f"\n搜索: {query}")
        processor.test_similarity_search(query, top_k=2)
    
    # 清理测试文件
    try:
        os.remove(test_csv_file)
        print(f"\n🗑️ 已清理测试文件: {test_csv_file}")
    except:
        pass
    
    print("\n✅ 增量处理测试完成！")
    
    return final_count - original_count


def show_database_summary():
    """显示数据库概要信息"""
    print("\n📋 向量数据库概要信息")
    print("="*40)
    
    if not os.path.exists("news_vectors"):
        print("❌ 向量数据库不存在")
        return
    
    db = SimpleVectorDB("news_vectors")
    stats = db.get_stats()
    
    print(f"📊 总文档数: {stats['total_documents']}")
    print(f"🔢 向量维度: {stats['vector_dimension']}")
    print(f"📁 存储路径: {stats['storage_path']}")
    print(f"📅 创建时间: {stats.get('created_at', '未知')}")
    print(f"🔄 最后更新: {stats.get('last_updated', '未知')}")
    
    # 显示一些示例文档
    if stats['total_documents'] > 0:
        print(f"\n📰 最近的几条新闻标题:")
        doc_ids = db.doc_ids[-5:]  # 最后5个文档
        for i, doc_id in enumerate(doc_ids, 1):
            if doc_id in db.metadata:
                title = db.metadata[doc_id].get('title', '无标题')
                print(f"   {i}. {title[:50]}...")


if __name__ == "__main__":
    print("增量向量数据库处理测试")
    print("="*60)
    
    # 显示当前数据库状态
    show_database_summary()
    
    # 执行增量处理测试
    added_count = test_incremental_processing()
    
    # 显示最终数据库状态
    show_database_summary()
    
    print(f"\n🎉 测试完成！成功添加了 {added_count} 条新闻到向量数据库")
