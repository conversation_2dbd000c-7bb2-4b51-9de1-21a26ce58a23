#!/bin/bash

# 新闻处理主流水线 - 快速启动脚本
# 适用于 Linux/macOS

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

# 检查Python
check_python() {
    if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
        print_error "未找到Python，请先安装Python 3.8+"
        exit 1
    fi
    
    # 优先使用python3
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    else
        PYTHON_CMD="python"
    fi
    
    # 检查Python版本
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    print_info "使用Python版本: $PYTHON_VERSION"
}

# 显示菜单
show_menu() {
    clear
    print_header "========================================"
    print_header "   新闻处理主流水线 - 快速启动"
    print_header "========================================"
    echo
    echo "请选择运行模式:"
    echo
    echo "1. 快速测试 (每个来源5条新闻)"
    echo "2. 标准处理 (每个来源10条新闻)"
    echo "3. 大量处理 (每个来源50条新闻)"
    echo "4. 全量处理 (所有可用新闻)"
    echo "5. 自定义参数"
    echo "6. 查看帮助"
    echo "0. 退出"
    echo
}

# 执行流水线
run_pipeline() {
    local args="$1"
    echo
    print_info "🚀 执行命令: $PYTHON_CMD master_pipeline.py $args"
    echo
    
    if $PYTHON_CMD master_pipeline.py $args; then
        echo
        print_success "执行完成"
        echo
        print_info "💡 现在可以使用搜索功能:"
        print_info "   $PYTHON_CMD news_search.py"
    else
        echo
        print_error "执行失败，请检查错误信息"
    fi
}

# 快速测试
quick_test() {
    print_info "🚀 执行快速测试..."
    run_pipeline "5 true --test-search"
}

# 标准处理
standard() {
    print_info "🚀 执行标准处理..."
    run_pipeline "10 true"
}

# 大量处理
large() {
    print_info "🚀 执行大量处理..."
    run_pipeline "50 true"
}

# 全量处理
full() {
    print_info "🚀 执行全量处理..."
    run_pipeline "all true --test-search"
}

# 自定义参数
custom() {
    echo
    print_header "自定义参数设置:"
    echo
    
    read -p "新闻数量 (数字或all, 默认10): " news_count
    news_count=${news_count:-10}
    
    read -p "获取详情 (true/false, 默认true): " get_detail
    get_detail=${get_detail:-true}
    
    read -p "搜索测试 (y/n, 默认n): " test_search
    test_search_param=""
    if [[ "$test_search" =~ ^[Yy]$ ]]; then
        test_search_param="--test-search"
    fi
    
    read -p "保留文件 (y/n, 默认n): " no_cleanup
    cleanup_param=""
    if [[ "$no_cleanup" =~ ^[Yy]$ ]]; then
        cleanup_param="--no-cleanup"
    fi
    
    print_info "🚀 执行自定义处理..."
    run_pipeline "$news_count $get_detail $test_search_param $cleanup_param"
}

# 显示帮助
show_help() {
    echo
    print_header "📖 使用帮助:"
    echo
    $PYTHON_CMD master_pipeline.py --help
    echo
    read -p "按回车键继续..."
}

# 主函数
main() {
    # 检查Python
    check_python
    
    while true; do
        show_menu
        read -p "请输入选择 (0-6): " choice
        
        case $choice in
            1)
                quick_test
                ;;
            2)
                standard
                ;;
            3)
                large
                ;;
            4)
                full
                ;;
            5)
                custom
                ;;
            6)
                show_help
                continue
                ;;
            0)
                echo
                print_info "👋 再见！"
                exit 0
                ;;
            *)
                echo
                print_error "无效选择，请重新输入"
                sleep 1
                continue
                ;;
        esac
        
        echo
        read -p "按回车键返回菜单..."
    done
}

# 运行主函数
main
