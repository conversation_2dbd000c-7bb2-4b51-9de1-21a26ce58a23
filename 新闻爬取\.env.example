# 新闻向量数据库系统配置文件
# 复制此文件为 .env 并填入你的实际配置

# ================================
# 向量化API配置 (必填)
# ================================

# 项目ID - 从你的API提供商获取
PROJECT_ID=your_project_id_here

# EasyLLM模型ID - 指定使用的嵌入模型
EASYLLM_ID=your_easyllm_id_here

# API密钥 - 用于身份验证
API_KEY=your_api_key_here

# API基础URL - 向量化服务的端点
BASE_URL=https://api.example.com/v1/projects/{project_id}/embeddings

# ================================
# 向量化参数配置 (可选)
# ================================

# 向量维度 - 生成的向量维度大小
DIMENSIONS=768

# API调用限制 - 每分钟最大请求次数
RATE_LIMIT=60

# ================================
# 数据库配置 (可选)
# ================================

# 向量数据库存储路径
VECTOR_DB_PATH=news_vectors

# 批处理大小 - 每批处理的新闻数量
BATCH_SIZE=10

# 最大线程数 - 并发处理线程数
MAX_WORKERS=3

# ================================
# 爬虫配置 (可选)
# ================================

# 请求延迟 - 爬虫请求间隔(秒)
CRAWL_DELAY=1

# 用户代理 - 爬虫请求头
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

# 超时设置 - 请求超时时间(秒)
REQUEST_TIMEOUT=30

# ================================
# 质量控制配置 (可选)
# ================================

# 最小内容长度 - 新闻内容最小字符数
MIN_CONTENT_LENGTH=20

# 质量阈值 - 新闻质量评分最小值
QUALITY_THRESHOLD=0.3

# 相似度阈值 - 搜索结果最小相似度
SIMILARITY_THRESHOLD=0.3

# ================================
# 日志配置 (可选)
# ================================

# 日志级别 - DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE=logs/news_processor.log

# ================================
# 配置说明
# ================================

# 1. 必填项目必须配置，否则系统无法正常运行
# 2. 可选项目有默认值，可根据需要调整
# 3. API相关配置请联系你的API提供商获取
# 4. 路径配置支持相对路径和绝对路径
# 5. 数值配置请根据你的硬件性能和网络状况调整

# ================================
# 安全提醒
# ================================

# ⚠️ 重要：请勿将包含真实API密钥的.env文件提交到版本控制系统
# ⚠️ 建议：将.env文件添加到.gitignore中
# ⚠️ 提示：定期更换API密钥以确保安全
