# 新闻处理主流水线使用指南

## 概述

`master_pipeline.py` 是一个完整的一键式自动化脚本，实现从新闻爬取到向量数据库的端到端处理流程。

## 功能特性

### 🔄 完整流水线
1. **环境检查** - 验证Python版本、依赖包、配置文件
2. **新闻爬取** - 调用统一爬虫获取最新新闻
3. **向量化处理** - 将新闻内容转换为向量
4. **数据库验证** - 确保向量数据库正常工作
5. **搜索测试** - 验证搜索功能（可选）
6. **文件清理** - 清理中间文件（可选）

### 🛡️ 错误处理
- 详细的错误日志和报告
- 关键步骤失败时自动停止
- 非关键步骤失败时继续执行
- 完整的执行报告生成

### 📊 性能监控
- 每个步骤的执行时间统计
- 处理速度计算
- 成功率统计
- 详细的性能报告

## 使用方法

### 基本语法
```bash
python master_pipeline.py [新闻数量] [获取详情] [选项]
```

### 参数说明

#### 位置参数
- `新闻数量`: 每个来源爬取的新闻数量
  - 数字: 如 `10`, `50`, `100`
  - `all`: 爬取所有可用新闻
  - 默认: `10`

- `获取详情`: 是否获取新闻详细内容
  - `true`: 获取详细内容（推荐）
  - `false`: 只获取标题和摘要
  - 默认: `true`

#### 可选参数
- `--no-cleanup`: 不清理中间文件
- `--test-search`: 运行搜索功能测试
- `--db-path PATH`: 自定义向量数据库路径
- `--verbose, -v`: 显示详细输出
- `--version`: 显示版本信息
- `--help, -h`: 显示帮助信息

## 使用示例

### 1. 基本使用
```bash
# 每个来源爬取10条新闻，获取详细内容
python master_pipeline.py

# 等同于
python master_pipeline.py 10 true
```

### 2. 大量数据处理
```bash
# 每个来源爬取50条新闻，获取详细内容
python master_pipeline.py 50 true

# 爬取所有可用新闻
python master_pipeline.py all true
```

### 3. 快速测试
```bash
# 每个来源5条新闻，不获取详细内容，运行搜索测试
python master_pipeline.py 5 false --test-search
```

### 4. 保留中间文件
```bash
# 处理后不清理CSV文件
python master_pipeline.py 20 true --no-cleanup
```

### 5. 自定义数据库
```bash
# 使用自定义数据库路径
python master_pipeline.py 30 true --db-path my_custom_db
```

### 6. 完整功能测试
```bash
# 运行完整测试，包括搜索功能
python master_pipeline.py 15 true --test-search --no-cleanup
```

## 执行流程详解

### 步骤1: 环境检查
- ✅ 检查Python版本（需要3.8+）
- ✅ 验证必要文件存在
- ✅ 检查配置文件（.env）
- ✅ 验证依赖包安装

### 步骤2: 新闻爬取
- 🕷️ 调用 `unified_news_crawler.py`
- 📰 从4个新闻源并发爬取
- 📄 生成统一格式CSV文件
- 📊 统计爬取结果

### 步骤3: 向量化处理
- 🔢 调用 `csv_to_vector_processor.py`
- 🧠 使用Embedding API生成向量
- 💾 存储到向量数据库
- 🔄 跳过重复和低质量内容

### 步骤4: 数据库验证
- 🔍 检查数据库文件完整性
- 📊 验证文档数量和向量维度
- ✅ 确保数据库可正常访问

### 步骤5: 搜索测试（可选）
- 🔍 执行多个测试查询
- 📈 验证搜索结果质量
- 🎯 测试相似度计算

### 步骤6: 文件清理（可选）
- 🧹 归档已处理的CSV文件
- 🗑️ 删除临时文件
- 📦 整理工作目录

## 输出说明

### 控制台输出
```
🚀 新闻处理主流水线启动
================================================================================
📋 配置信息:
   📰 新闻数量: 10
   📝 获取详情: true
   🧹 清理文件: true
   🔍 搜索测试: false
   💾 向量数据库: news_vectors
   ⏰ 开始时间: 2025-07-07 18:00:00
================================================================================

🔧 步骤1: 环境检查和配置验证
--------------------------------------------------
🐍 Python版本: 3.11.0
   ✅ unified_news_crawler.py
   ✅ csv_to_vector_processor.py
   ✅ vector_database.py
   ✅ config.py
   ✅ news_search.py
✅ 环境检查通过

📰 步骤2: 爬取新闻数据
--------------------------------------------------
🚀 执行命令: python unified_news_crawler.py 10 true
✅ 新闻爬取完成
   📁 生成文件: unified_news_20250707_180000.csv
   📊 爬取数量: 40 条
   ⏱️  耗时: 45.2 秒

🔢 步骤3: 向量化处理
--------------------------------------------------
🔧 初始化向量处理器...
📄 处理文件 1/1: unified_news_20250707_180000.csv
✅ 向量化处理完成
   📁 处理文件: 1/1
   📊 向量化数量: 38 条
   ⏱️  耗时: 67.8 秒

🔍 步骤4: 验证向量数据库
--------------------------------------------------
✅ 向量数据库验证成功
   📊 总文档数: 684
   🔢 向量维度: 768
   💾 存储路径: news_vectors

🧹 步骤6: 清理中间文件
--------------------------------------------------
   📦 归档: unified_news_20250707_180000.csv
✅ 文件清理完成
   📁 处理文件: 1 个

================================================================================
📊 执行报告
================================================================================
🕐 开始时间: 2025-07-07 18:00:00
🕐 结束时间: 2025-07-07 18:02:15
⏱️  总耗时: 135.3 秒

📈 性能统计:
   🕷️  爬取耗时: 45.2 秒
   🔢 向量化耗时: 67.8 秒
   📊 爬取速度: 0.88 条/秒
   📊 向量化速度: 0.56 条/秒

📊 数据统计:
   📰 爬取新闻: 40 条
   🔢 向量化新闻: 38 条
   📁 处理文件: 1 个
   ✅ 成功率: 95.0%

🎯 最终状态: ✅ 成功
================================================================================

🎉 流水线执行成功！
💡 现在可以使用以下命令进行新闻搜索:
   python news_search.py
```

### 生成的文件

1. **向量数据库**: `news_vectors/`
   - `vectors.pkl`: 向量数据
   - `metadata.db`: 元数据数据库
   - `indexes.pkl`: 索引文件
   - `stats.json`: 统计信息

2. **执行报告**: `data/logs/pipeline_report_YYYYMMDD_HHMMSS.json`
   ```json
   {
     "timestamp": "20250707_180000",
     "start_time": "2025-07-07T18:00:00",
     "end_time": "2025-07-07T18:02:15",
     "total_time_seconds": 135.3,
     "news_crawled": 40,
     "news_vectorized": 38,
     "success": true,
     "errors": []
   }
   ```

3. **归档文件**: `data/archive/`
   - 已处理的CSV文件会移动到此目录

## 错误处理

### 常见错误及解决方案

1. **配置文件缺失**
   ```
   ❌ 环境检查失败: 配置不完整，请检查.env文件
   ```
   **解决**: 创建.env文件并配置API密钥

2. **依赖包缺失**
   ```
   ❌ 环境检查失败: 缺少依赖包: requests
   ```
   **解决**: `pip install requests pandas numpy`

3. **爬虫执行失败**
   ```
   ❌ 新闻爬取失败: 爬虫返回码: 1
   ```
   **解决**: 检查网络连接和爬虫脚本

4. **向量化失败**
   ```
   ❌ 向量化处理失败: API请求失败
   ```
   **解决**: 检查Embedding API配置和网络

### 调试技巧

1. **使用详细输出**
   ```bash
   python master_pipeline.py 5 true --verbose
   ```

2. **保留中间文件**
   ```bash
   python master_pipeline.py 10 true --no-cleanup
   ```

3. **检查日志文件**
   ```bash
   cat data/logs/pipeline_report_*.json
   ```

## 性能优化建议

### 1. 批量处理
- 建议每次处理20-50条新闻
- 避免一次性处理过多数据

### 2. 网络优化
- 确保网络连接稳定
- 考虑使用代理加速API访问

### 3. 存储优化
- 定期清理归档文件
- 监控磁盘空间使用

### 4. 并发控制
- 系统会自动控制并发数
- 避免同时运行多个实例

## 后续使用

### 搜索新闻
```bash
python news_search.py
```

### 查看数据库状态
```bash
python -c "from vector_database import SimpleVectorDB; db = SimpleVectorDB('news_vectors'); print(db.get_stats())"
```

### 定期更新
```bash
# 建议每天运行一次
python master_pipeline.py 20 true --test-search
```

## 技术支持

如遇问题，请检查：
1. 📋 配置文件是否正确
2. 🌐 网络连接是否正常
3. 💾 磁盘空间是否充足
4. 📄 日志文件中的错误信息
