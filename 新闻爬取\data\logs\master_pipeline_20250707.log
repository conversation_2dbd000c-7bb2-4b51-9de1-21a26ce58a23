2025-07-07 18:29:22 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-07 18:30:19 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-07 18:30:37 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-07 18:30:37 - MasterPipeline - INFO - 🔍 运行搜索演示: 人工智能
2025-07-07 18:37:22 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-07 18:37:39 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-07 18:37:39 - MasterPipeline - INFO - 🚀 开始执行完整新闻处理流水线
2025-07-07 18:37:39 - MasterPipeline - INFO - ⚙️ 配置参数: max_news=5, detail=True, all=False
2025-07-07 18:37:39 - MasterPipeline - INFO - 📰 阶段1: 开始新闻爬取
2025-07-07 18:37:39 - MasterPipeline - INFO -    参数: max_news=5, detail=True, all=False
2025-07-07 18:37:39 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 1/3)
2025-07-07 18:37:39 - MasterPipeline - WARNING - ⚠️ 新闻爬取失败 (尝试 1/3): module 'signal' has no attribute 'SIGALRM'
2025-07-07 18:37:39 - MasterPipeline - INFO - ⏳ 等待 5 秒后重试...
2025-07-07 18:37:44 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 2/3)
2025-07-07 18:37:44 - MasterPipeline - WARNING - ⚠️ 新闻爬取失败 (尝试 2/3): module 'signal' has no attribute 'SIGALRM'
2025-07-07 18:37:44 - MasterPipeline - INFO - ⏳ 等待 5 秒后重试...
2025-07-07 18:37:49 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 3/3)
2025-07-07 18:37:49 - MasterPipeline - WARNING - ⚠️ 新闻爬取失败 (尝试 3/3): module 'signal' has no attribute 'SIGALRM'
2025-07-07 18:37:49 - MasterPipeline - ERROR - ❌ 新闻爬取最终失败，已达到最大重试次数
2025-07-07 18:37:49 - MasterPipeline - ERROR - ❌ 阶段1失败: module 'signal' has no attribute 'SIGALRM'
2025-07-07 18:37:49 - MasterPipeline - ERROR - ❌ 流水线执行失败: module 'signal' has no attribute 'SIGALRM'
2025-07-07 18:37:49 - MasterPipeline - ERROR - 错误详情: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\处理为存储在向量数据库里\新闻爬取\master_pipeline.py", line 219, in run_full_pipeline
    csv_file, news_count = self._stage_1_crawling(max_news_per_source, get_detail, get_all)
                           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\处理为存储在向量数据库里\新闻爬取\master_pipeline.py", line 283, in _stage_1_crawling
    csv_file, news_count, news_data = self._execute_with_retry(
                                      ~~~~~~~~~~~~~~~~~~~~~~~~^
        self.crawler.crawl_all,
        ^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        operation_name="新闻爬取"
        ^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\处理为存储在向量数据库里\新闻爬取\master_pipeline.py", line 509, in _execute_with_retry
    signal.signal(signal.SIGALRM, timeout_handler)
                  ^^^^^^^^^^^^^^
AttributeError: module 'signal' has no attribute 'SIGALRM'. Did you mean: 'SIGABRT'?

2025-07-07 18:37:49 - MasterPipeline - INFO - ⏱️ 总执行时间: 10.01 秒
2025-07-07 18:38:47 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-07 18:38:47 - MasterPipeline - INFO - 🚀 开始执行完整新闻处理流水线
2025-07-07 18:38:47 - MasterPipeline - INFO - ⚙️ 配置参数: max_news=2, detail=True, all=False
2025-07-07 18:38:47 - MasterPipeline - INFO - 📰 阶段1: 开始新闻爬取
2025-07-07 18:38:47 - MasterPipeline - INFO -    参数: max_news=2, detail=True, all=False
2025-07-07 18:38:47 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 1/3)
2025-07-07 18:39:24 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-07 18:39:24 - MasterPipeline - INFO - 🚀 开始执行完整新闻处理流水线
2025-07-07 18:39:24 - MasterPipeline - INFO - ⚙️ 配置参数: max_news=10, detail=True, all=False
2025-07-07 18:39:24 - MasterPipeline - INFO - 📰 阶段1: 开始新闻爬取
2025-07-07 18:39:24 - MasterPipeline - INFO -    参数: max_news=10, detail=True, all=False
2025-07-07 18:39:24 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 1/3)
2025-07-07 18:40:56 - MasterPipeline - INFO - ✅ 新闻爬取成功
2025-07-07 18:40:56 - MasterPipeline - WARNING - ⚠️ 发现 7 个质量问题
2025-07-07 18:40:56 - MasterPipeline - WARNING -    - 新闻16缺少media字段
2025-07-07 18:40:56 - MasterPipeline - WARNING -    - 新闻17缺少media字段
2025-07-07 18:40:56 - MasterPipeline - WARNING -    - 新闻18缺少media字段
2025-07-07 18:40:56 - MasterPipeline - WARNING -    - 新闻19缺少media字段
2025-07-07 18:40:56 - MasterPipeline - WARNING -    - 新闻20缺少media字段
2025-07-07 18:40:56 - MasterPipeline - INFO - ✅ 爬取结果验证通过: 40条新闻, 文件大小89646字节
2025-07-07 18:40:56 - MasterPipeline - INFO - ✅ 阶段1完成: 爬取 40 条新闻，保存到 unified_news_20250707_184056.csv
2025-07-07 18:40:56 - MasterPipeline - INFO - 🔄 阶段2: 开始向量化处理
2025-07-07 18:40:56 - MasterPipeline - INFO -    处理文件: unified_news_20250707_184056.csv
2025-07-07 18:40:56 - MasterPipeline - INFO - 🔄 执行向量化处理 (尝试 1/2)
2025-07-07 18:40:56 - MasterPipeline - INFO - ✅ 向量化处理成功
2025-07-07 18:40:56 - MasterPipeline - ERROR - ❌ 阶段2失败: 'CSVNewsVectorProcessor' object has no attribute 'get_stats'
2025-07-07 18:40:56 - MasterPipeline - ERROR - ❌ 流水线执行失败: 'CSVNewsVectorProcessor' object has no attribute 'get_stats'
2025-07-07 18:40:56 - MasterPipeline - ERROR - 错误详情: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\处理为存储在向量数据库里\新闻爬取\master_pipeline.py", line 222, in run_full_pipeline
    processed_count = self._stage_2_processing(csv_file)
  File "C:\Users\<USER>\Desktop\处理为存储在向量数据库里\新闻爬取\master_pipeline.py", line 361, in _stage_2_processing
    processor_stats = self.processor.get_stats()
                      ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'CSVNewsVectorProcessor' object has no attribute 'get_stats'

2025-07-07 18:40:56 - MasterPipeline - INFO - ⏱️ 总执行时间: 92.19 秒
2025-07-07 18:44:41 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-07 18:44:49 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-07 18:44:49 - MasterPipeline - INFO - 🚀 开始执行完整新闻处理流水线
2025-07-07 18:44:49 - MasterPipeline - INFO - ⚙️ 配置参数: max_news=1, detail=True, all=False
2025-07-07 18:44:49 - MasterPipeline - INFO - 📰 阶段1: 开始新闻爬取
2025-07-07 18:44:49 - MasterPipeline - INFO -    参数: max_news=1, detail=True, all=False
2025-07-07 18:44:49 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 1/3)
2025-07-07 18:45:22 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-07 18:45:22 - MasterPipeline - INFO - 🚀 开始执行完整新闻处理流水线
2025-07-07 18:45:22 - MasterPipeline - INFO - ⚙️ 配置参数: max_news=10, detail=True, all=False
2025-07-07 18:45:22 - MasterPipeline - INFO - 📰 阶段1: 开始新闻爬取
2025-07-07 18:45:22 - MasterPipeline - INFO -    参数: max_news=10, detail=True, all=False
2025-07-07 18:45:22 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 1/3)
2025-07-07 18:46:52 - MasterPipeline - INFO - ✅ 新闻爬取成功
2025-07-07 18:46:52 - MasterPipeline - WARNING - ⚠️ 发现 7 个质量问题
2025-07-07 18:46:52 - MasterPipeline - WARNING -    - 新闻16缺少media字段
2025-07-07 18:46:52 - MasterPipeline - WARNING -    - 新闻17缺少media字段
2025-07-07 18:46:52 - MasterPipeline - WARNING -    - 新闻18缺少media字段
2025-07-07 18:46:52 - MasterPipeline - WARNING -    - 新闻19缺少media字段
2025-07-07 18:46:52 - MasterPipeline - WARNING -    - 新闻20缺少media字段
2025-07-07 18:46:52 - MasterPipeline - INFO - ✅ 爬取结果验证通过: 40条新闻, 文件大小89116字节
2025-07-07 18:46:52 - MasterPipeline - INFO - ✅ 阶段1完成: 爬取 40 条新闻，保存到 unified_news_20250707_184652.csv
2025-07-07 18:46:52 - MasterPipeline - INFO - 🔄 阶段2: 开始向量化处理
2025-07-07 18:46:52 - MasterPipeline - INFO -    处理文件: unified_news_20250707_184652.csv
2025-07-07 18:46:52 - MasterPipeline - INFO - 🔄 执行向量化处理 (尝试 1/2)
2025-07-07 18:46:52 - MasterPipeline - INFO - ✅ 向量化处理成功
2025-07-07 18:46:52 - MasterPipeline - ERROR - ❌ 阶段2失败: 没有成功处理任何新闻
2025-07-07 18:46:52 - MasterPipeline - ERROR - ❌ 流水线执行失败: 没有成功处理任何新闻
2025-07-07 18:46:52 - MasterPipeline - ERROR - 错误详情: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\处理为存储在向量数据库里\新闻爬取\master_pipeline.py", line 222, in run_full_pipeline
    processed_count = self._stage_2_processing(csv_file)
  File "C:\Users\<USER>\Desktop\处理为存储在向量数据库里\新闻爬取\master_pipeline.py", line 365, in _stage_2_processing
    self._validate_processing_results(processed_count, processor_stats)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\处理为存储在向量数据库里\新闻爬取\master_pipeline.py", line 565, in _validate_processing_results
    raise ValueError("没有成功处理任何新闻")
ValueError: 没有成功处理任何新闻

2025-07-07 18:46:52 - MasterPipeline - INFO - ⏱️ 总执行时间: 90.20 秒
2025-07-07 18:51:01 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-07 18:51:01 - MasterPipeline - INFO - 🚀 开始执行完整新闻处理流水线
2025-07-07 18:51:01 - MasterPipeline - INFO - ⚙️ 配置参数: max_news=1, detail=True, all=False
2025-07-07 18:51:01 - MasterPipeline - INFO - 📰 阶段1: 开始新闻爬取
2025-07-07 18:51:01 - MasterPipeline - INFO -    参数: max_news=1, detail=True, all=False
2025-07-07 18:51:01 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 1/3)
2025-07-07 18:51:55 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-07 18:51:55 - MasterPipeline - INFO - 🚀 开始执行完整新闻处理流水线
2025-07-07 18:51:55 - MasterPipeline - INFO - ⚙️ 配置参数: max_news=None, detail=True, all=True
2025-07-07 18:51:55 - MasterPipeline - INFO - 📰 阶段1: 开始新闻爬取
2025-07-07 18:51:55 - MasterPipeline - INFO -    参数: max_news=30, detail=True, all=True
2025-07-07 18:51:55 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 1/3)
2025-07-07 19:31:33 - MasterPipeline - INFO - ✅ 新闻爬取成功
2025-07-07 19:31:33 - MasterPipeline - WARNING - ⚠️ 发现 347 个质量问题
2025-07-07 19:31:33 - MasterPipeline - WARNING -    - 新闻375内容过短(0字符)
2025-07-07 19:31:33 - MasterPipeline - WARNING -    - 新闻375缺少content字段
2025-07-07 19:31:33 - MasterPipeline - WARNING -    - 新闻653缺少media字段
2025-07-07 19:31:33 - MasterPipeline - WARNING -    - 新闻654缺少media字段
2025-07-07 19:31:33 - MasterPipeline - WARNING -    - 新闻655缺少media字段
2025-07-07 19:31:33 - MasterPipeline - INFO - ✅ 爬取结果验证通过: 1219条新闻, 文件大小3010878字节
2025-07-07 19:31:33 - MasterPipeline - INFO - ✅ 阶段1完成: 爬取 1219 条新闻，保存到 unified_news_20250707_193133.csv
2025-07-07 19:31:33 - MasterPipeline - INFO - 🔄 阶段2: 开始向量化处理
2025-07-07 19:31:33 - MasterPipeline - INFO -    处理文件: unified_news_20250707_193133.csv
2025-07-07 19:31:33 - MasterPipeline - INFO - 🔄 执行向量化处理 (尝试 1/2)
2025-07-07 19:35:23 - MasterPipeline - INFO - ✅ 向量化处理成功
2025-07-07 19:35:23 - MasterPipeline - INFO - ✅ 处理结果验证通过: 成功1071条, 失败0条
2025-07-07 19:35:23 - MasterPipeline - INFO - ✅ 阶段2完成: 处理 1071 条新闻向量
2025-07-07 19:35:23 - MasterPipeline - INFO - 💾 阶段3: 开始数据库存储验证
2025-07-07 19:35:23 - MasterPipeline - INFO -    数据库路径: C:\Users\<USER>\Desktop\处理为存储在向量数据库里\新闻爬取\news_vectors
2025-07-07 19:35:23 - MasterPipeline - INFO -    存储文档数: 1071
2025-07-07 19:35:23 - MasterPipeline - ERROR - ❌ 阶段3失败: 'VectorDatabase' object has no attribute 'verify_database_integrity'
2025-07-07 19:35:23 - MasterPipeline - ERROR - ❌ 流水线执行失败: 'VectorDatabase' object has no attribute 'verify_database_integrity'
2025-07-07 19:35:23 - MasterPipeline - ERROR - 错误详情: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\处理为存储在向量数据库里\新闻爬取\master_pipeline.py", line 225, in run_full_pipeline
    stored_count = self._stage_3_storage_verification()
  File "C:\Users\<USER>\Desktop\处理为存储在向量数据库里\新闻爬取\master_pipeline.py", line 416, in _stage_3_storage_verification
    self._validate_storage_results(stored_count, db_stats)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\处理为存储在向量数据库里\新闻爬取\master_pipeline.py", line 592, in _validate_storage_results
    if not self.vector_db.verify_database_integrity():
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'VectorDatabase' object has no attribute 'verify_database_integrity'

2025-07-07 19:35:23 - MasterPipeline - INFO - ⏱️ 总执行时间: 2608.58 秒
2025-07-07 19:38:52 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-07 19:42:08 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-07 19:42:08 - MasterPipeline - INFO - 🔍 运行搜索演示: 人工智能
2025-07-07 19:48:24 - MasterPipeline - INFO - 🚀 主流水线初始化完成
