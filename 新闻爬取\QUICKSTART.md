# 🚀 快速开始指南

## 5分钟上手新闻向量数据库系统

### 第一步：环境准备

```bash
# 1. 确保Python 3.8+已安装
python --version

# 2. 安装依赖包
pip install pandas requests numpy sqlite3

# 3. 检查文件结构
ls -la
```

### 第二步：配置API

创建 `.env` 文件：

```env
PROJECT_ID=your_project_id
EASYLLM_ID=your_easyllm_id
API_KEY=your_api_key_here
BASE_URL=https://api.example.com/v1/projects/{project_id}/embeddings
DIMENSIONS=768
RATE_LIMIT=60
```

### 第三步：爬取新闻

```bash
# 爬取网易新闻（推荐）
python netease_news_crawler.py

# 或者爬取新浪新闻
python sina_news_crawler.py
```

### 第四步：向量化处理

```bash
# 一键处理CSV文件到向量数据库
python csv_to_vector_processor.py
```

### 第五步：测试搜索

```python
# 在Python中测试
from csv_to_vector_processor import CSVNewsVectorProcessor
from config import get_embedding_config

processor = CSVNewsVectorProcessor(get_embedding_config(), "news_vectors")
processor.test_similarity_search("人工智能", top_k=3)
```

## 🎯 预期结果

运行成功后你将看到：

```
✅ 成功加载 648 条新闻记录
✅ 成功向量化: 646
✅ 向量数据库统计:
   总文档数: 646
   向量维度: 768
   存储路径: news_vectors

🔍 搜索测试结果:
1. 相似度: 0.763
   📰 人工智能技术发展迅速
```

## 📁 生成的文件

```
news_vectors/           # 向量数据库目录
├── vectors.pkl        # 向量数据
├── metadata.db        # 新闻元数据
├── indexes.pkl        # 搜索索引
└── stats.json         # 统计信息
```

## 🔄 日常使用

```bash
# 1. 爬取新新闻
python netease_news_crawler.py

# 2. 自动处理并追加到数据库
python csv_to_vector_processor.py

# 3. 搜索测试
python -c "
from csv_to_vector_processor import CSVNewsVectorProcessor
from config import get_embedding_config
processor = CSVNewsVectorProcessor(get_embedding_config(), 'news_vectors')
processor.test_similarity_search('你的搜索词', top_k=5)
"
```

## ❓ 遇到问题？

1. **API配置错误**: 检查 `.env` 文件配置
2. **找不到CSV**: 先运行爬虫生成数据
3. **向量化失败**: 检查网络连接和API密钥
4. **搜索无结果**: 确认数据库中有相关内容

## 📖 更多信息

- 详细文档: [README.md](README.md)
- 测试脚本: `test_incremental_processing.py`
- 配置说明: `config.py`

---

🎉 **恭喜！你已经成功搭建了一个智能新闻向量数据库系统！**
