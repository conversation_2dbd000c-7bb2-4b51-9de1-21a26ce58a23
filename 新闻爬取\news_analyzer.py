#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻分析工具
提供新闻数据的深度分析功能
"""

import os
import json
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from typing import List, Dict, Tuple
from vector_database import VectorDatabase
from config import SystemConfig

# matplotlib是可选的，如果没有安装也能正常工作
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib import rcParams
    # 设置中文字体
    rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    rcParams['axes.unicode_minus'] = False
    HAS_MATPLOTLIB = True
except ImportError:
    print("⚠️ matplotlib未安装，图表功能将被禁用")
    print("💡 如需图表功能，请运行: pip install matplotlib")
    HAS_MATPLOTLIB = False

class NewsAnalyzer:
    """新闻分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.config = SystemConfig()
        self.vector_db = VectorDatabase(self.config.VECTOR_DB_PATH)
        
        print(f"📊 新闻分析器初始化完成")
        print(f"   数据库路径: {self.config.VECTOR_DB_PATH}")
        print(f"   总新闻数: {len(self.vector_db.doc_ids)}")
        print()
    
    def analyze_news_trends(self) -> Dict:
        """分析新闻趋势"""
        print("📈 分析新闻趋势...")
        
        # 按日期统计
        date_counts = defaultdict(int)
        category_trends = defaultdict(lambda: defaultdict(int))
        media_trends = defaultdict(lambda: defaultdict(int))
        
        for doc_id in self.vector_db.doc_ids:
            metadata = self.vector_db.metadata.get(doc_id, {})
            
            # 提取日期
            formatted_time = metadata.get('formatted_time', '')
            if formatted_time:
                try:
                    date_str = formatted_time.split()[0]  # 获取日期部分
                    date_counts[date_str] += 1
                    
                    # 分类趋势
                    category = metadata.get('category', '未知')
                    category_trends[category][date_str] += 1
                    
                    # 媒体趋势
                    media = metadata.get('media', '未知')
                    media_trends[media][date_str] += 1
                    
                except Exception:
                    pass
        
        return {
            'date_counts': dict(date_counts),
            'category_trends': dict(category_trends),
            'media_trends': dict(media_trends)
        }
    
    def analyze_content_quality(self) -> Dict:
        """分析内容质量"""
        print("⭐ 分析内容质量...")
        
        quality_scores = []
        quality_by_category = defaultdict(list)
        quality_by_media = defaultdict(list)
        
        for doc_id in self.vector_db.doc_ids:
            metadata = self.vector_db.metadata.get(doc_id, {})
            quality_score = metadata.get('quality_score', 0.0)
            
            quality_scores.append(quality_score)
            
            category = metadata.get('category', '未知')
            quality_by_category[category].append(quality_score)
            
            media = metadata.get('media', '未知')
            quality_by_media[media].append(quality_score)
        
        # 计算统计信息
        if quality_scores:
            avg_quality = sum(quality_scores) / len(quality_scores)
            max_quality = max(quality_scores)
            min_quality = min(quality_scores)
        else:
            avg_quality = max_quality = min_quality = 0.0
        
        # 按分类计算平均质量
        category_avg_quality = {}
        for category, scores in quality_by_category.items():
            if scores:
                category_avg_quality[category] = sum(scores) / len(scores)
        
        # 按媒体计算平均质量
        media_avg_quality = {}
        for media, scores in quality_by_media.items():
            if scores:
                media_avg_quality[media] = sum(scores) / len(scores)
        
        return {
            'overall': {
                'average': avg_quality,
                'maximum': max_quality,
                'minimum': min_quality,
                'total_count': len(quality_scores)
            },
            'by_category': category_avg_quality,
            'by_media': media_avg_quality
        }
    
    def find_hot_topics(self, top_k: int = 10) -> List[Dict]:
        """发现热门话题"""
        print(f"🔥 发现热门话题 (Top {top_k})...")
        
        # 提取关键词（简单实现：从标题中提取）
        word_counts = Counter()
        topic_news = defaultdict(list)
        
        for doc_id in self.vector_db.doc_ids:
            metadata = self.vector_db.metadata.get(doc_id, {})
            title = metadata.get('title', '')
            
            # 简单的关键词提取（可以改进为更复杂的NLP方法）
            words = [word for word in title if len(word) >= 2]
            for word in words:
                word_counts[word] += 1
                topic_news[word].append({
                    'doc_id': doc_id,
                    'title': title,
                    'metadata': metadata
                })
        
        # 获取热门话题
        hot_topics = []
        for word, count in word_counts.most_common(top_k):
            if count >= 2:  # 至少出现2次
                hot_topics.append({
                    'topic': word,
                    'count': count,
                    'news': topic_news[word][:5]  # 只显示前5条相关新闻
                })
        
        return hot_topics
    
    def analyze_similar_clusters(self, threshold: float = 0.8) -> List[Dict]:
        """分析相似新闻聚类"""
        print(f"🔗 分析相似新闻聚类 (阈值: {threshold})...")
        
        clusters = []
        processed_docs = set()
        
        for doc_id in self.vector_db.doc_ids:
            if doc_id in processed_docs:
                continue
            
            # 查找相似文档
            similar_docs = self.vector_db.find_similar_documents(
                doc_id, top_k=10, threshold=threshold
            )
            
            if len(similar_docs) > 1:  # 至少有2个相似文档
                cluster = {
                    'main_doc': doc_id,
                    'main_metadata': self.vector_db.metadata.get(doc_id, {}),
                    'similar_docs': similar_docs,
                    'cluster_size': len(similar_docs)
                }
                clusters.append(cluster)
                
                # 标记已处理的文档
                processed_docs.add(doc_id)
                for similar_doc in similar_docs:
                    processed_docs.add(similar_doc.get('doc_id', ''))
        
        return sorted(clusters, key=lambda x: x['cluster_size'], reverse=True)

    def create_text_chart(self, data: Dict, title: str, max_width: int = 50) -> str:
        """创建简单的文本图表"""
        if not data:
            return f"{title}: 无数据\n"

        lines = [f"\n📊 {title}"]
        lines.append("=" * len(title))

        # 找到最大值用于缩放
        max_value = max(data.values()) if data.values() else 1

        for key, value in sorted(data.items(), key=lambda x: x[1], reverse=True):
            # 计算条形图长度
            bar_length = int((value / max_value) * max_width) if max_value > 0 else 0
            bar = "█" * bar_length

            # 格式化显示
            lines.append(f"{key:15} │{bar:<{max_width}} {value}")

        lines.append("")
        return "\n".join(lines)

    def generate_report(self, output_file: str = None) -> str:
        """生成分析报告"""
        print("📋 生成分析报告...")
        
        # 收集分析数据
        trends = self.analyze_news_trends()
        quality = self.analyze_content_quality()
        hot_topics = self.find_hot_topics()
        clusters = self.analyze_similar_clusters()
        
        # 生成报告
        report_lines = []
        report_lines.append("📊 新闻数据分析报告")
        report_lines.append("=" * 50)
        report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"数据库路径: {self.config.VECTOR_DB_PATH}")
        report_lines.append(f"总新闻数: {len(self.vector_db.doc_ids)}")
        report_lines.append("")
        
        # 内容质量分析
        report_lines.append("⭐ 内容质量分析")
        report_lines.append("-" * 30)
        overall_quality = quality['overall']
        report_lines.append(f"平均质量分: {overall_quality['average']:.3f}")
        report_lines.append(f"最高质量分: {overall_quality['maximum']:.3f}")
        report_lines.append(f"最低质量分: {overall_quality['minimum']:.3f}")
        report_lines.append("")
        
        # 分类质量分析
        if quality['by_category']:
            report_lines.append("📂 各分类平均质量:")
            for category, avg_score in sorted(quality['by_category'].items(), 
                                            key=lambda x: x[1], reverse=True):
                report_lines.append(f"   {category}: {avg_score:.3f}")
            report_lines.append("")
        
        # 热门话题
        if hot_topics:
            report_lines.append("🔥 热门话题")
            report_lines.append("-" * 30)
            for i, topic in enumerate(hot_topics[:10], 1):
                report_lines.append(f"{i}. {topic['topic']} ({topic['count']} 次)")
            report_lines.append("")
        
        # 相似聚类
        if clusters:
            report_lines.append("🔗 相似新闻聚类")
            report_lines.append("-" * 30)
            for i, cluster in enumerate(clusters[:5], 1):
                main_title = cluster['main_metadata'].get('title', '无标题')
                report_lines.append(f"{i}. 聚类大小: {cluster['cluster_size']}")
                report_lines.append(f"   主要新闻: {main_title}")
            report_lines.append("")
        
        # 数据趋势
        date_counts = trends['date_counts']
        if date_counts:
            report_lines.append("📈 新闻发布趋势")
            report_lines.append("-" * 30)
            for date, count in sorted(date_counts.items()):
                report_lines.append(f"{date}: {count} 条")

            # 添加文本图表
            chart = self.create_text_chart(date_counts, "日期分布图表")
            report_lines.append(chart)

        # 分类分布图表
        if quality['by_category']:
            chart = self.create_text_chart(quality['by_category'], "分类质量分布图表")
            report_lines.append(chart)
        
        report_content = "\n".join(report_lines)
        
        # 保存报告
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"✅ 报告已保存到: {output_file}")
        
        return report_content

def main():
    """主函数"""
    analyzer = NewsAnalyzer()
    
    # 生成分析报告
    report_file = f"data/analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    report = analyzer.generate_report(report_file)
    
    print("\n" + "=" * 50)
    print("📋 分析报告预览:")
    print("=" * 50)
    print(report)

if __name__ == "__main__":
    main()
