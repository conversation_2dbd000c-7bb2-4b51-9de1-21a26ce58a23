#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
凤凰网新闻爬虫适配器
将凤凰网爬虫的数据格式转换为统一标准格式
"""

import sys
import os
from datetime import datetime
from ifeng_news_crawler import IfengNewsCrawler


class IfengNewsAdapter:
    """凤凰网新闻适配器"""
    
    def __init__(self, max_news=50, get_detail=True, get_all=False):
        """
        初始化适配器
        :param max_news: 最大新闻数量
        :param get_detail: 是否获取详细内容
        :param get_all: 是否获取全部新闻
        """
        self.max_news = max_news
        self.get_detail = get_detail
        self.get_all = get_all
        self.crawler = IfengNewsCrawler(max_news, get_detail, get_all)
    
    def crawl(self):
        """
        执行爬取并返回标准格式数据
        :return: (news_list, count) 元组
        """
        # 静默爬取，不保存文件
        raw_news_list = self.crawler.crawl_without_save()

        if not raw_news_list:
            return [], 0

        # 转换为标准格式
        standard_news_list = []
        for i, raw_item in enumerate(raw_news_list, 1):
            standard_item = self.convert_to_standard_format(raw_item, i)
            if standard_item:
                standard_news_list.append(standard_item)
        return standard_news_list, len(standard_news_list)
    
    def convert_to_standard_format(self, raw_item, rank):
        """
        将凤凰网原始数据转换为标准格式
        原始格式: {'title': '...', 'url': '...', 'time': '...', 'source': '...', 'content': '...'}
        """
        try:
            # 解析时间
            create_date, create_time, formatted_time = self.parse_time(raw_item.get('time', ''))
            
            # 提取分类（从来源或URL中推断）
            category = self.extract_category(raw_item.get('source', ''), raw_item.get('url', ''))
            
            return {
                'id': rank,
                'title': raw_item.get('title', '').strip(),
                'url': raw_item.get('url', ''),
                'media': '凤凰网',
                'create_date': create_date,
                'create_time': create_time,
                'formatted_time': formatted_time,
                'category': category,
                'ranking_type': '热点新闻',
                'rank': rank,
                'comment_url': '',
                'top_num': '',
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'content': raw_item.get('content', '').strip()[:2000]  # 限制长度
            }
        except Exception as e:
            # 静默处理错误
            return None
    
    def parse_time(self, time_str):
        """解析时间字符串"""
        try:
            if not time_str:
                now = datetime.now()
                return now.strftime('%Y-%m-%d'), now.strftime('%H:%M:%S'), now.strftime('%m-%d %H:%M')
            
            # 处理各种时间格式
            time_str = time_str.strip()
            
            # 格式1: 2024-07-07 15:30
            if '-' in time_str and ':' in time_str:
                if len(time_str.split()) == 2:  # 有日期和时间
                    dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M')
                    return dt.strftime('%Y-%m-%d'), dt.strftime('%H:%M:%S'), dt.strftime('%m-%d %H:%M')
                elif len(time_str.split('-')) == 3:  # 只有日期
                    dt = datetime.strptime(time_str, '%Y-%m-%d')
                    return dt.strftime('%Y-%m-%d'), '00:00:00', dt.strftime('%m-%d')
            
            # 格式2: 今天 15:30, 昨天 15:30
            if '今天' in time_str or '昨天' in time_str:
                now = datetime.now()
                if '昨天' in time_str:
                    from datetime import timedelta
                    now = now - timedelta(days=1)
                
                time_part = time_str.replace('今天', '').replace('昨天', '').strip()
                if ':' in time_part:
                    hour, minute = time_part.split(':')
                    dt = now.replace(hour=int(hour), minute=int(minute), second=0)
                    return dt.strftime('%Y-%m-%d'), dt.strftime('%H:%M:%S'), dt.strftime('%m-%d %H:%M')
                else:
                    return now.strftime('%Y-%m-%d'), '00:00:00', now.strftime('%m-%d')
            
            # 格式3: 15:30 (今天的时间)
            if ':' in time_str and len(time_str.split(':')) == 2:
                now = datetime.now()
                hour, minute = time_str.split(':')
                dt = now.replace(hour=int(hour), minute=int(minute), second=0)
                return dt.strftime('%Y-%m-%d'), dt.strftime('%H:%M:%S'), dt.strftime('%m-%d %H:%M')
            
            # 默认使用当前时间
            now = datetime.now()
            return now.strftime('%Y-%m-%d'), now.strftime('%H:%M:%S'), now.strftime('%m-%d %H:%M')
            
        except Exception as e:
            # 静默处理时间解析错误
            now = datetime.now()
            return now.strftime('%Y-%m-%d'), now.strftime('%H:%M:%S'), now.strftime('%m-%d %H:%M')
    
    def extract_category(self, source, url):
        """从来源或URL中提取分类"""
        try:
            # 根据来源推断分类
            source_lower = source.lower() if source else ''
            url_lower = url.lower() if url else ''
            
            # 科技相关
            if any(keyword in source_lower or keyword in url_lower for keyword in 
                   ['科技', 'tech', 'ai', '人工智能', '互联网', '数码']):
                return '科技'
            
            # 财经相关
            if any(keyword in source_lower or keyword in url_lower for keyword in 
                   ['财经', 'finance', '经济', '股市', '投资']):
                return '财经'
            
            # 体育相关
            if any(keyword in source_lower or keyword in url_lower for keyword in 
                   ['体育', 'sports', '足球', '篮球', '奥运']):
                return '体育'
            
            # 娱乐相关
            if any(keyword in source_lower or keyword in url_lower for keyword in 
                   ['娱乐', 'ent', '明星', '电影', '音乐']):
                return '娱乐'
            
            # 汽车相关
            if any(keyword in source_lower or keyword in url_lower for keyword in 
                   ['汽车', 'auto', '车市', '新能源车']):
                return '汽车'
            
            # 默认分类
            return '综合'
            
        except Exception as e:
            # 静默处理分类提取错误
            return '综合'


def main():
    """测试适配器"""
    adapter = IfengNewsAdapter(max_news=10, get_detail=True, get_all=False)
    news_list, count = adapter.crawl()
    
    print(f"\n=== 凤凰网适配器测试结果 ===")
    print(f"获取新闻数量: {count}")
    
    if news_list:
        print(f"\n前3条新闻示例:")
        for i, news in enumerate(news_list[:3], 1):
            print(f"\n{i}. {news['title']}")
            print(f"   媒体: {news['media']}")
            print(f"   分类: {news['category']}")
            print(f"   时间: {news['formatted_time']}")
            print(f"   内容: {news['content'][:100]}...")


if __name__ == "__main__":
    main()
