# 新闻向量数据库系统

一个完整的新闻爬取、向量化处理和语义搜索系统，支持多源新闻数据的统一向量化存储和高效检索。

## 🌟 项目特色

- **多源新闻爬取**: 支持网易新闻、新浪新闻等多个新闻网站
- **智能向量化**: 使用先进的文本嵌入技术将新闻转换为向量
- **增量数据库**: 统一的向量数据库，支持新数据自动追加
- **语义搜索**: 基于向量相似度的智能新闻检索
- **重复检测**: 自动识别并跳过重复新闻
- **多线程处理**: 高效的并发处理能力
- **持久化存储**: 数据永久保存，支持系统重启

## 📁 项目结构

```
新闻爬取/
├── README.md                    # 项目说明文档
├── config.py                    # 配置文件管理
├── .env                        # 环境变量配置
├── vector_database.py          # 向量数据库核心实现
├── csv_to_vector_processor.py  # CSV向量化处理器
├── netease_news_crawler.py     # 网易新闻爬虫
├── sina_news_crawler.py        # 新浪新闻爬虫
├── unified_news_crawler.py     # 统一新闻爬虫
├── test_incremental_processing.py  # 增量处理测试
└── news_vectors/               # 向量数据库存储目录
    ├── vectors.pkl             # 向量数据
    ├── metadata.db             # 元数据数据库
    ├── indexes.pkl             # 索引文件
    └── stats.json              # 统计信息
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install pandas requests numpy sqlite3 pickle threading

# 创建环境配置文件
cp .env.example .env
```

### 2. 配置设置

编辑 `.env` 文件，配置向量化API：

```env
# 向量化API配置
PROJECT_ID=your_project_id
EASYLLM_ID=your_easyllm_id  
API_KEY=your_api_key
BASE_URL=https://api.example.com/v1/projects/{project_id}/embeddings
DIMENSIONS=768
RATE_LIMIT=60
```

### 3. 爬取新闻

```bash
# 爬取网易新闻
python netease_news_crawler.py

# 爬取新浪新闻  
python sina_news_crawler.py

# 使用统一爬虫
python unified_news_crawler.py
```

### 4. 向量化处理

```bash
# 处理CSV文件并存储到向量数据库
python csv_to_vector_processor.py
```

## 📊 系统架构

### 数据流程

```mermaid
graph LR
    A[新闻网站] --> B[爬虫系统]
    B --> C[CSV文件]
    C --> D[向量化处理]
    D --> E[向量数据库]
    E --> F[语义搜索]
    
    D --> G[重复检测]
    G --> H[跳过重复]
    G --> I[新增数据]
    I --> E
```

### 核心组件

1. **新闻爬虫模块**
   - 多网站支持
   - 结构化数据提取
   - CSV格式输出

2. **向量化处理模块**
   - 文本清洗和质量评估
   - 批量向量化处理
   - 多线程并发处理

3. **向量数据库模块**
   - 高效向量存储
   - 相似度检索
   - 元数据管理
   - 持久化存储

## 🔧 详细功能

### 新闻爬取

支持多个新闻网站的数据爬取：

- **网易新闻**: 热点新闻、分类新闻
- **新浪新闻**: 实时新闻、排行榜
- **扩展性**: 易于添加新的新闻源

### 向量化处理

- **文本预处理**: 清洗HTML标签、特殊字符
- **质量评估**: 基于内容长度、结构完整性评分
- **批量处理**: 支持大规模数据的高效处理
- **API限流**: 智能控制API调用频率

### 向量数据库

- **存储格式**: 
  - 向量数据: PKL格式
  - 元数据: SQLite数据库
  - 索引: 优化检索性能
  
- **功能特性**:
  - 增量添加
  - 重复检测
  - 相似度搜索
  - 统计信息

## 📈 使用示例

### 基本使用

```python
from csv_to_vector_processor import CSVNewsVectorProcessor
from config import get_embedding_config

# 创建处理器
config = get_embedding_config()
processor = CSVNewsVectorProcessor(config, "news_vectors")

# 处理CSV文件
processor.process_csv_file("news_data.csv")

# 搜索相关新闻
processor.test_similarity_search("人工智能发展", top_k=5)
```

### 增量处理

```python
# 系统会自动检测重复并只处理新数据
processor.process_csv_file("new_news_data.csv")
```

### 搜索功能

```python
# 语义搜索
results = processor.vector_db.search(query_vector, top_k=10, threshold=0.5)

for result in results:
    print(f"相似度: {result['similarity']:.3f}")
    print(f"标题: {result['metadata']['title']}")
    print(f"内容: {result['metadata']['content'][:100]}...")
```

## 📊 性能指标

### 处理能力

- **向量化速度**: ~10条新闻/分钟 (受API限制)
- **存储效率**: 768维向量，约3KB/条新闻
- **搜索速度**: <100ms (646条新闻数据库)
- **并发处理**: 支持3线程并发

### 搜索精度

- **精确匹配**: 相似度 > 0.75
- **相关匹配**: 相似度 0.6-0.75  
- **一般相关**: 相似度 0.5-0.6
- **不相关**: 相似度 < 0.5

## 🔍 测试验证

### 运行测试

```bash
# 增量处理测试
python test_incremental_processing.py

# 搜索精度测试
python -c "
from csv_to_vector_processor import CSVNewsVectorProcessor
from config import get_embedding_config
processor = CSVNewsVectorProcessor(get_embedding_config(), 'news_vectors')
processor.test_similarity_search('测试查询', top_k=3)
"
```

### 测试结果示例

```
📊 处理完成统计
============================================================
总处理数量: 648
成功向量化: 646
向量化失败: 0
跳过低质量: 2
跳过重复: 0
成功率: 99.7%

向量数据库统计:
总文档数: 646
向量维度: 768
存储路径: news_vectors
```

## 🛠️ 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| PROJECT_ID | 项目ID | 必填 |
| EASYLLM_ID | 模型ID | 必填 |
| API_KEY | API密钥 | 必填 |
| BASE_URL | API地址 | 必填 |
| DIMENSIONS | 向量维度 | 768 |
| RATE_LIMIT | 限流(次/分钟) | 60 |

### 处理参数

```python
processor.process_csv_file(
    csv_file_path="news.csv",    # CSV文件路径
    batch_size=10,               # 批处理大小
    max_news=None,               # 最大处理数量
    max_workers=3                # 线程数
)
```

## 📝 开发指南

### 添加新的新闻源

1. 创建新的爬虫文件
2. 继承基础爬虫类
3. 实现数据提取逻辑
4. 输出标准CSV格式

### 扩展向量数据库

1. 修改 `vector_database.py`
2. 添加新的索引类型
3. 实现自定义搜索算法
4. 优化存储格式

## 🚨 注意事项

1. **API限制**: 注意向量化API的调用频率限制
2. **存储空间**: 大量数据需要足够的磁盘空间
3. **内存使用**: 向量数据会加载到内存中
4. **数据备份**: 定期备份向量数据库文件

## 📊 当前状态

- **数据库大小**: 649条新闻
- **向量维度**: 768
- **存储路径**: `news_vectors/`
- **最后更新**: 2025-07-07

## 🔧 故障排除

### 常见问题

**Q: 向量化API调用失败**
```
A: 检查.env配置文件中的API密钥和URL是否正确
   确认API服务是否正常运行
   检查网络连接状态
```

**Q: CSV文件找不到**
```
A: 确保CSV文件在正确的目录下
   检查文件名格式是否正确
   运行爬虫生成新的CSV文件
```

**Q: 向量数据库加载失败**
```
A: 检查news_vectors目录是否存在
   确认文件权限设置正确
   尝试删除损坏的数据库文件重新创建
```

**Q: 搜索结果不准确**
```
A: 增加数据库中的新闻数量
   调整搜索阈值参数
   优化查询关键词
```

### 日志分析

系统会输出详细的处理日志：
- ✅ 成功操作
- ❌ 错误信息
- ⚠️ 警告提示
- 📊 统计数据

## 🔄 系统维护

### 定期维护任务

1. **数据库备份**
```bash
# 备份向量数据库
cp -r news_vectors news_vectors_backup_$(date +%Y%m%d)
```

2. **清理临时文件**
```bash
# 清理CSV文件（如果需要）
rm *.csv

# 清理缓存文件
rm -rf __pycache__
```

3. **性能监控**
```python
# 检查数据库状态
from vector_database import SimpleVectorDB
db = SimpleVectorDB("news_vectors")
stats = db.get_stats()
print(f"文档数量: {stats['total_documents']}")
print(f"存储大小: {stats.get('storage_size', 'N/A')}")
```

### 数据库优化

- **索引重建**: 定期重建索引提高搜索性能
- **数据压缩**: 压缩旧数据减少存储空间
- **分片存储**: 大数据量时考虑分片存储

## 📈 扩展功能

### 计划中的功能

- [ ] 实时新闻推送
- [ ] 新闻分类标签
- [ ] 情感分析集成
- [ ] 多语言支持
- [ ] Web界面
- [ ] API服务
- [ ] 数据可视化
- [ ] 自动摘要生成

### 集成建议

1. **Web应用**: 使用Flask/Django构建搜索界面
2. **API服务**: 提供RESTful API接口
3. **数据分析**: 集成Jupyter Notebook进行数据分析
4. **监控系统**: 添加系统监控和告警

## 🤝 贡献指南

### 如何贡献

1. Fork项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 代码规范

- 使用Python PEP 8编码规范
- 添加适当的注释和文档字符串
- 编写单元测试
- 更新相关文档

### 报告问题

请在GitHub Issues中报告问题，包含：
- 详细的问题描述
- 复现步骤
- 系统环境信息
- 错误日志

## 📊 版本历史

### v1.0.0 (2025-07-07)
- ✅ 基础新闻爬取功能
- ✅ 向量化处理系统
- ✅ 统一向量数据库
- ✅ 语义搜索功能
- ✅ 增量数据处理
- ✅ 重复检测机制

### 未来版本计划
- v1.1.0: Web界面和API服务
- v1.2.0: 多语言支持
- v1.3.0: 实时处理能力

## 📞 联系方式

- **项目地址**: [GitHub Repository]
- **问题反馈**: [GitHub Issues]
- **邮箱**: <EMAIL>

## 📄 许可证

MIT License

Copyright (c) 2025 新闻向量数据库系统

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

---

**项目维护者**: 新闻向量数据库团队
**创建时间**: 2025-07-07
**最后更新**: 2025-07-07
**当前版本**: v1.0.0
