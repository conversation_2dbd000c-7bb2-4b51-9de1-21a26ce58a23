"""
新闻向量数据库查询脚本
提供多种查询方式和交互界面
"""

import sys
import os
import json
import argparse
from datetime import datetime
from typing import List, Dict, Optional

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from csv_to_vector_processor import CSVNewsVectorProcessor
    from config import get_embedding_config
    from vector_database import SimpleVectorDB
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("💡 请确保相关文件在当前目录")
    sys.exit(1)


class NewsSearchEngine:
    """新闻搜索引擎"""
    
    def __init__(self, db_path: str = "news_vectors"):
        """初始化搜索引擎"""
        self.db_path = db_path
        self.config = get_embedding_config()
        
        # 检查数据库是否存在
        if not os.path.exists(db_path):
            print(f"❌ 向量数据库不存在: {db_path}")
            print("💡 请先运行 csv_to_vector_processor.py 创建数据库")
            sys.exit(1)
        
        # 初始化处理器
        self.processor = CSVNewsVectorProcessor(self.config, db_path)
        self.db = self.processor.vector_db
        
        print(f"✅ 向量数据库加载成功")
        self.show_database_info()
    
    def show_database_info(self):
        """显示数据库基本信息"""
        stats = self.db.get_stats()
        print(f"📊 数据库信息:")
        print(f"   总文档数: {stats['total_documents']}")
        print(f"   向量维度: {stats['vector_dimension']}")
        print(f"   存储路径: {stats['storage_path']}")
        print(f"   最后更新: {stats.get('last_updated', '未知')}")
        print()
    
    def search_news(self, query: str, top_k: int = 5, threshold: float = 0.3, 
                   category_filter: str = None, media_filter: str = None,
                   date_filter: str = None) -> List[Dict]:
        """
        搜索新闻
        
        Args:
            query: 搜索查询
            top_k: 返回结果数量
            threshold: 相似度阈值
            category_filter: 分类过滤
            media_filter: 媒体过滤
            date_filter: 日期过滤 (YYYY-MM-DD)
        """
        print(f"🔍 搜索查询: {query}")
        print(f"📊 参数: top_k={top_k}, threshold={threshold}")
        
        if category_filter:
            print(f"🏷️ 分类过滤: {category_filter}")
        if media_filter:
            print(f"📰 媒体过滤: {media_filter}")
        if date_filter:
            print(f"📅 日期过滤: {date_filter}")
        
        print("-" * 50)
        
        # 获取查询向量
        query_embedding = self.processor.get_embeddings([query])
        
        if not query_embedding:
            print("❌ 获取查询向量失败")
            return []
        
        # 执行搜索
        results = self.db.search(query_embedding[0], top_k=top_k*2, threshold=threshold)
        
        # 应用过滤器
        filtered_results = []
        for result in results:
            metadata = result['metadata']
            
            # 分类过滤
            if category_filter and category_filter.lower() not in metadata.get('category', '').lower():
                continue
            
            # 媒体过滤
            if media_filter and media_filter.lower() not in metadata.get('media', '').lower():
                continue
            
            # 日期过滤
            if date_filter:
                publish_time = metadata.get('publish_time', '')
                if date_filter not in publish_time:
                    continue
            
            filtered_results.append(result)
            
            if len(filtered_results) >= top_k:
                break
        
        # 显示结果
        if filtered_results:
            print(f"✅ 找到 {len(filtered_results)} 条相关新闻:")
            for i, result in enumerate(filtered_results, 1):
                self.display_news_result(result, i)
        else:
            print("❌ 未找到符合条件的新闻")
            print("💡 建议:")
            print("   - 尝试更宽泛的搜索词")
            print("   - 降低相似度阈值")
            print("   - 移除过滤条件")
        
        return filtered_results
    
    def display_news_result(self, result: Dict, rank: int):
        """显示单条新闻结果"""
        metadata = result['metadata']
        similarity = result['similarity']
        
        print(f"\n{rank}. 相似度: {similarity:.3f}")
        print(f"   📰 {metadata['title']}")
        print(f"   🏷️  {metadata.get('category', '未知')} | 📰 {metadata.get('media', '未知')}")
        print(f"   📅 {metadata.get('publish_time', '未知时间')}")
        
        # 显示内容摘要
        content = metadata.get('content', '')
        if content:
            summary = content[:100] + "..." if len(content) > 100 else content
            print(f"   📝 {summary}")
        
        # 显示URL（如果有）
        if 'url' in metadata:
            print(f"   🔗 {metadata['url']}")
    
    def get_categories(self) -> List[str]:
        """获取所有分类"""
        categories = set()
        for doc_id in self.db.doc_ids:
            if doc_id in self.db.metadata:
                category = self.db.metadata[doc_id].get('category', '')
                if category:
                    categories.add(category)
        return sorted(list(categories))
    
    def get_media_sources(self) -> List[str]:
        """获取所有媒体来源"""
        media_sources = set()
        for doc_id in self.db.doc_ids:
            if doc_id in self.db.metadata:
                media = self.db.metadata[doc_id].get('media', '')
                if media:
                    media_sources.add(media)
        return sorted(list(media_sources))
    
    def show_statistics(self):
        """显示数据库统计信息"""
        print("📊 数据库详细统计")
        print("=" * 40)
        
        # 基本统计
        stats = self.db.get_stats()
        print(f"总文档数: {stats['total_documents']}")
        print(f"向量维度: {stats['vector_dimension']}")
        
        # 分类统计
        categories = {}
        media_sources = {}
        dates = {}
        
        for doc_id in self.db.doc_ids:
            if doc_id in self.db.metadata:
                metadata = self.db.metadata[doc_id]
                
                # 统计分类
                category = metadata.get('category', '未知')
                categories[category] = categories.get(category, 0) + 1
                
                # 统计媒体
                media = metadata.get('media', '未知')
                media_sources[media] = media_sources.get(media, 0) + 1
                
                # 统计日期
                publish_time = metadata.get('publish_time', '')
                if publish_time:
                    date = publish_time.split(' ')[0]  # 提取日期部分
                    dates[date] = dates.get(date, 0) + 1
        
        # 显示分类统计
        print(f"\n📂 分类分布:")
        for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            print(f"   {category}: {count}")
        
        # 显示媒体统计
        print(f"\n📰 媒体来源:")
        for media, count in sorted(media_sources.items(), key=lambda x: x[1], reverse=True):
            print(f"   {media}: {count}")
        
        # 显示日期统计（最近10天）
        print(f"\n📅 最近日期分布:")
        recent_dates = sorted(dates.items(), key=lambda x: x[0], reverse=True)[:10]
        for date, count in recent_dates:
            print(f"   {date}: {count}")
    
    def interactive_search(self):
        """交互式搜索模式"""
        print("\n🔍 进入交互式搜索模式")
        print("=" * 40)
        print("💡 输入 'help' 查看帮助")
        print("💡 输入 'quit' 退出")
        print()
        
        while True:
            try:
                query = input("🔍 请输入搜索词: ").strip()
                
                if not query:
                    continue
                
                if query.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                if query.lower() == 'help':
                    self.show_help()
                    continue
                
                if query.lower() == 'stats':
                    self.show_statistics()
                    continue
                
                if query.lower() == 'categories':
                    categories = self.get_categories()
                    print(f"📂 可用分类: {', '.join(categories)}")
                    continue
                
                if query.lower() == 'media':
                    media = self.get_media_sources()
                    print(f"📰 媒体来源: {', '.join(media)}")
                    continue
                
                # 解析搜索参数
                parts = query.split(' --')
                search_query = parts[0].strip()
                
                # 默认参数
                top_k = 5
                threshold = 0.3
                category_filter = None
                media_filter = None
                date_filter = None
                
                # 解析参数
                for part in parts[1:]:
                    if part.startswith('top_k='):
                        top_k = int(part.split('=')[1])
                    elif part.startswith('threshold='):
                        threshold = float(part.split('=')[1])
                    elif part.startswith('category='):
                        category_filter = part.split('=')[1]
                    elif part.startswith('media='):
                        media_filter = part.split('=')[1]
                    elif part.startswith('date='):
                        date_filter = part.split('=')[1]
                
                # 执行搜索
                self.search_news(search_query, top_k, threshold, 
                               category_filter, media_filter, date_filter)
                print()
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 搜索出错: {e}")
                continue
    
    def show_help(self):
        """显示帮助信息"""
        print("\n📖 搜索帮助")
        print("=" * 30)
        print("基本搜索:")
        print("  人工智能")
        print()
        print("高级搜索 (使用 -- 分隔参数):")
        print("  人工智能 --top_k=10 --threshold=0.5")
        print("  新能源汽车 --category=科技 --media=网易新闻")
        print("  疫情防控 --date=2025-07-07")
        print()
        print("可用参数:")
        print("  --top_k=N        返回结果数量 (默认5)")
        print("  --threshold=X    相似度阈值 (默认0.3)")
        print("  --category=分类   按分类过滤")
        print("  --media=媒体     按媒体过滤")
        print("  --date=日期      按日期过滤 (YYYY-MM-DD)")
        print()
        print("特殊命令:")
        print("  help      显示此帮助")
        print("  stats     显示数据库统计")
        print("  categories 显示所有分类")
        print("  media     显示所有媒体")
        print("  quit      退出程序")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="新闻向量数据库查询工具")
    parser.add_argument("--db", default="news_vectors", help="数据库路径")
    parser.add_argument("--query", "-q", help="搜索查询")
    parser.add_argument("--top_k", "-k", type=int, default=5, help="返回结果数量")
    parser.add_argument("--threshold", "-t", type=float, default=0.3, help="相似度阈值")
    parser.add_argument("--category", "-c", help="分类过滤")
    parser.add_argument("--media", "-m", help="媒体过滤")
    parser.add_argument("--date", "-d", help="日期过滤")
    parser.add_argument("--interactive", "-i", action="store_true", help="交互式模式")
    parser.add_argument("--stats", "-s", action="store_true", help="显示统计信息")
    
    args = parser.parse_args()
    
    # 创建搜索引擎
    search_engine = NewsSearchEngine(args.db)
    
    if args.stats:
        search_engine.show_statistics()
        return
    
    if args.interactive:
        search_engine.interactive_search()
        return
    
    if args.query:
        search_engine.search_news(
            args.query, 
            args.top_k, 
            args.threshold,
            args.category,
            args.media,
            args.date
        )
    else:
        print("🔍 新闻向量数据库查询工具")
        print("=" * 40)
        print("使用方法:")
        print("  python news_search_query.py -q '搜索词'")
        print("  python news_search_query.py -i  # 交互式模式")
        print("  python news_search_query.py -s  # 显示统计")
        print("  python news_search_query.py --help  # 查看帮助")


    def export_results(self, results: List[Dict], filename: str = None):
        """导出搜索结果"""
        if not results:
            print("❌ 没有结果可导出")
            return

        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"search_results_{timestamp}.json"

        export_data = []
        for result in results:
            export_data.append({
                'similarity': result['similarity'],
                'title': result['metadata']['title'],
                'content': result['metadata'].get('content', ''),
                'category': result['metadata'].get('category', ''),
                'media': result['metadata'].get('media', ''),
                'publish_time': result['metadata'].get('publish_time', ''),
                'url': result['metadata'].get('url', '')
            })

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            print(f"✅ 结果已导出到: {filename}")
        except Exception as e:
            print(f"❌ 导出失败: {e}")

    def batch_search(self, queries: List[str], output_file: str = None):
        """批量搜索"""
        print(f"🔍 开始批量搜索 {len(queries)} 个查询...")

        all_results = {}
        for i, query in enumerate(queries, 1):
            print(f"\n[{i}/{len(queries)}] 搜索: {query}")
            results = self.search_news(query, top_k=3, threshold=0.3)
            all_results[query] = results

        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(all_results, f, ensure_ascii=False, indent=2, default=str)
                print(f"✅ 批量搜索结果已保存到: {output_file}")
            except Exception as e:
                print(f"❌ 保存失败: {e}")

        return all_results

    def find_similar_news(self, news_id: str, top_k: int = 5):
        """根据新闻ID查找相似新闻"""
        # 查找指定新闻
        target_doc_id = None
        for doc_id in self.db.doc_ids:
            if news_id in doc_id:
                target_doc_id = doc_id
                break

        if not target_doc_id:
            print(f"❌ 未找到新闻ID: {news_id}")
            return []

        if target_doc_id not in self.db.vectors:
            print(f"❌ 新闻向量不存在: {news_id}")
            return []

        # 使用该新闻的向量进行搜索
        target_vector = self.db.vectors[target_doc_id]
        results = self.db.search(target_vector, top_k=top_k+1, threshold=0.3)

        # 移除自身
        filtered_results = [r for r in results if r['doc_id'] != target_doc_id][:top_k]

        print(f"🔍 查找与新闻ID '{news_id}' 相似的新闻:")
        print(f"📰 原新闻: {self.db.metadata[target_doc_id]['title']}")
        print("-" * 50)

        if filtered_results:
            print(f"✅ 找到 {len(filtered_results)} 条相似新闻:")
            for i, result in enumerate(filtered_results, 1):
                self.display_news_result(result, i)
        else:
            print("❌ 未找到相似新闻")

        return filtered_results


if __name__ == "__main__":
    main()
