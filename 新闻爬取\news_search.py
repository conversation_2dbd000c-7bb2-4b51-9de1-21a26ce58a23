#!/usr/bin/env python3
"""
新闻向量数据库查询脚本
支持交互式查询、批量查询、高级筛选等功能
"""

import sys
import os
import json
import argparse
from datetime import datetime
from typing import List, Dict, Optional

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from csv_to_vector_processor import CSVNewsVectorProcessor
    from config import get_embedding_config
    from vector_database import SimpleVectorDB
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("💡 请确保相关文件在当前目录")
    sys.exit(1)


class NewsSearchEngine:
    """新闻搜索引擎"""
    
    def __init__(self, db_path: str = "news_vectors"):
        """初始化搜索引擎"""
        self.db_path = db_path
        self.config = get_embedding_config()
        
        # 检查数据库是否存在
        if not os.path.exists(db_path):
            print(f"❌ 向量数据库不存在: {db_path}")
            print("💡 请先运行 csv_to_vector_processor.py 创建数据库")
            sys.exit(1)
        
        # 初始化处理器和数据库
        self.processor = CSVNewsVectorProcessor(self.config, db_path)
        self.db = self.processor.vector_db
        
        print(f"✅ 向量数据库加载成功")
        stats = self.db.get_stats()
        print(f"   📊 总文档数: {stats['total_documents']}")
        print(f"   🔢 向量维度: {stats['vector_dimension']}")
        print(f"   📁 存储路径: {stats['storage_path']}")
    
    def search(self, query: str, top_k: int = 10, threshold: float = 0.3,
               category: str = None, media: str = None, date_range: tuple = None) -> List[Dict]:
        """
        搜索新闻
        
        Args:
            query: 搜索查询
            top_k: 返回结果数量
            threshold: 相似度阈值
            category: 新闻分类筛选
            media: 媒体来源筛选
            date_range: 日期范围筛选 (start_date, end_date)
        
        Returns:
            搜索结果列表
        """
        print(f"\n🔍 搜索查询: {query}")
        print(f"   参数: top_k={top_k}, threshold={threshold}")
        
        # 获取查询向量
        query_embedding = self.processor.get_embeddings([query])
        if not query_embedding:
            print("❌ 获取查询向量失败")
            return []
        
        # 执行向量搜索
        results = self.db.search(query_embedding[0], top_k=top_k*2, threshold=threshold)
        
        # 应用筛选条件
        filtered_results = self._apply_filters(results, category, media, date_range)
        
        # 限制结果数量
        final_results = filtered_results[:top_k]
        
        return final_results
    
    def _apply_filters(self, results: List[Dict], category: str = None, 
                      media: str = None, date_range: tuple = None) -> List[Dict]:
        """应用筛选条件"""
        filtered = []
        
        for result in results:
            metadata = result['metadata']
            
            # 分类筛选
            if category and metadata.get('category', '').lower() != category.lower():
                continue
            
            # 媒体筛选
            if media and media.lower() not in metadata.get('media', '').lower():
                continue
            
            # 日期筛选
            if date_range:
                try:
                    publish_time = metadata.get('publish_time', '')
                    if publish_time:
                        # 提取日期部分
                        date_str = publish_time.split()[0]  # 假设格式为 "2025-07-07 10:40:00"
                        if not (date_range[0] <= date_str <= date_range[1]):
                            continue
                except:
                    continue
            
            filtered.append(result)
        
        return filtered
    
    def display_results(self, results: List[Dict], show_content: bool = False):
        """显示搜索结果"""
        if not results:
            print("❌ 未找到相关新闻")
            return
        
        print(f"✅ 找到 {len(results)} 条相关新闻:")
        print("-" * 80)
        
        for i, result in enumerate(results, 1):
            metadata = result['metadata']
            similarity = result['similarity']
            
            print(f"\n{i}. 📰 {metadata.get('title', '无标题')}")
            print(f"   🎯 相似度: {similarity:.3f}")
            print(f"   🏷️  分类: {metadata.get('category', '未知')} | 📰 媒体: {metadata.get('media', '未知')}")
            print(f"   📅 时间: {metadata.get('publish_time', '未知')}")
            print(f"   🔗 链接: {metadata.get('url', '无链接')}")
            
            if show_content:
                content = metadata.get('content', '')
                if content:
                    # 显示内容摘要
                    summary = content[:200] + "..." if len(content) > 200 else content
                    print(f"   📝 内容: {summary}")
            
            print("-" * 80)
    
    def get_database_stats(self):
        """获取数据库统计信息"""
        stats = self.db.get_stats()
        
        print("\n📊 数据库统计信息")
        print("=" * 50)
        print(f"总文档数: {stats['total_documents']}")
        print(f"向量维度: {stats['vector_dimension']}")
        print(f"存储路径: {stats['storage_path']}")
        print(f"创建时间: {stats.get('created_at', '未知')}")
        print(f"最后更新: {stats.get('last_updated', '未知')}")
        
        # 分析分类分布
        categories = {}
        medias = {}
        
        for doc_id in self.db.doc_ids:
            if doc_id in self.db.metadata:
                metadata = self.db.metadata[doc_id]
                category = metadata.get('category', '未知')
                media = metadata.get('media', '未知')
                
                categories[category] = categories.get(category, 0) + 1
                medias[media] = medias.get(media, 0) + 1
        
        print(f"\n📈 分类分布:")
        for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"   {category}: {count}")
        
        print(f"\n📰 媒体分布:")
        for media, count in sorted(medias.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"   {media}: {count}")
    
    def interactive_search(self):
        """交互式搜索模式"""
        print("\n🚀 进入交互式搜索模式")
        print("💡 输入 'help' 查看帮助，输入 'quit' 退出")
        
        while True:
            try:
                query = input("\n🔍 请输入搜索关键词: ").strip()
                
                if not query:
                    continue
                
                if query.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                if query.lower() == 'help':
                    self._show_help()
                    continue
                
                if query.lower() == 'stats':
                    self.get_database_stats()
                    continue

                if query.lower().startswith('set top='):
                    try:
                        new_top = int(query.split('=')[1])
                        print(f"✅ 已设置默认显示数量为: {new_top}")
                        continue
                    except:
                        print("❌ 格式错误，请使用: set top=数字")
                        continue
                
                # 解析高级查询参数
                params = self._parse_query_params(query)
                
                # 执行搜索
                results = self.search(**params)
                
                # 显示结果
                self.display_results(results, show_content=True)
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 搜索出错: {e}")
    
    def _parse_query_params(self, query: str) -> Dict:
        """解析查询参数"""
        params = {
            'query': query,
            'top_k': 10,
            'threshold': 0.3,
            'category': None,
            'media': None,
            'date_range': None
        }
        
        # 简单的参数解析（可以扩展为更复杂的语法）
        if ' --' in query:
            parts = query.split(' --')
            params['query'] = parts[0].strip()
            
            for part in parts[1:]:
                if part.startswith('top='):
                    params['top_k'] = int(part.split('=')[1])
                elif part.startswith('threshold='):
                    params['threshold'] = float(part.split('=')[1])
                elif part.startswith('category='):
                    params['category'] = part.split('=')[1]
                elif part.startswith('media='):
                    params['media'] = part.split('=')[1]
        
        return params
    
    def _show_help(self):
        """显示帮助信息"""
        help_text = """
🔍 搜索帮助
============

基本搜索:
  人工智能                    # 搜索包含"人工智能"的新闻

高级搜索参数:
  --top=20                   # 返回前20条结果
  --threshold=0.5            # 设置相似度阈值为0.5
  --category=科技            # 筛选科技类新闻
  --media=网易新闻           # 筛选特定媒体

示例:
  人工智能 --top=20 --category=科技
  新能源汽车 --threshold=0.6 --media=网易
  电动汽车 --top=50          # 显示50条结果

特殊命令:
  help                       # 显示此帮助
  stats                      # 显示数据库统计
  set top=20                 # 设置默认显示数量
  quit/exit/q               # 退出程序

💡 提示:
  - 默认显示10条结果，可用 --top=数字 调整
  - 相似度越高越相关，建议阈值0.3-0.7
  - 使用具体关键词获得更好结果
        """
        print(help_text)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="新闻向量数据库查询工具")
    parser.add_argument("--db", default="news_vectors", help="数据库路径")
    parser.add_argument("--query", "-q", help="搜索查询")
    parser.add_argument("--top", "-t", type=int, default=10, help="返回结果数量")
    parser.add_argument("--threshold", type=float, default=0.3, help="相似度阈值")
    parser.add_argument("--category", "-c", help="分类筛选")
    parser.add_argument("--media", "-m", help="媒体筛选")
    parser.add_argument("--interactive", "-i", action="store_true", help="交互式模式")
    parser.add_argument("--stats", "-s", action="store_true", help="显示数据库统计")
    parser.add_argument("--content", action="store_true", help="显示新闻内容")
    parser.add_argument("--batch", "-b", help="批量查询文件路径")
    parser.add_argument("--output", "-o", help="批量查询结果输出文件")

    args = parser.parse_args()

    # 创建搜索引擎
    try:
        engine = NewsSearchEngine(args.db)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return

    # 显示统计信息
    if args.stats:
        engine.get_database_stats()
        return

    # 批量查询模式
    if args.batch:
        output_file = args.output or f"batch_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        engine.batch_search(args.batch, output_file)
        return

    # 交互式模式
    if args.interactive:
        engine.interactive_search()
        return

    # 单次查询模式
    if args.query:
        results = engine.search(
            query=args.query,
            top_k=args.top,
            threshold=args.threshold,
            category=args.category,
            media=args.media
        )
        engine.display_results(results, show_content=args.content)
    else:
        # 默认进入交互式模式
        engine.interactive_search()


    def batch_search(self, queries_file: str, output_file: str = None):
        """批量搜索"""
        print(f"📂 加载查询文件: {queries_file}")

        try:
            with open(queries_file, 'r', encoding='utf-8') as f:
                queries = [line.strip() for line in f if line.strip()]
        except Exception as e:
            print(f"❌ 读取查询文件失败: {e}")
            return

        print(f"📝 共 {len(queries)} 个查询")

        all_results = []

        for i, query in enumerate(queries, 1):
            print(f"\n🔍 [{i}/{len(queries)}] 搜索: {query}")

            results = self.search(query, top_k=3, threshold=0.3)

            batch_result = {
                'query': query,
                'timestamp': datetime.now().isoformat(),
                'results_count': len(results),
                'results': []
            }

            for result in results:
                batch_result['results'].append({
                    'title': result['metadata'].get('title', ''),
                    'similarity': result['similarity'],
                    'category': result['metadata'].get('category', ''),
                    'media': result['metadata'].get('media', ''),
                    'url': result['metadata'].get('url', ''),
                    'publish_time': result['metadata'].get('publish_time', '')
                })

            all_results.append(batch_result)

            # 显示简要结果
            if results:
                print(f"   ✅ 找到 {len(results)} 条结果")
                print(f"   📰 最佳匹配: {results[0]['metadata'].get('title', '')[:50]}...")
            else:
                print(f"   ❌ 未找到结果")

        # 保存结果
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(all_results, f, ensure_ascii=False, indent=2)
                print(f"\n💾 结果已保存到: {output_file}")
            except Exception as e:
                print(f"❌ 保存结果失败: {e}")

        # 显示汇总统计
        total_queries = len(all_results)
        successful_queries = sum(1 for r in all_results if r['results_count'] > 0)

        print(f"\n📊 批量搜索完成")
        print(f"   总查询数: {total_queries}")
        print(f"   成功查询: {successful_queries}")
        print(f"   成功率: {successful_queries/total_queries*100:.1f}%")


if __name__ == "__main__":
    main()
