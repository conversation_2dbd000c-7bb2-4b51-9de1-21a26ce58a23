# 新闻爬虫数据格式规范

## 📋 概述

本文档定义了新闻爬虫的标准数据格式和技术要求，确保新爬虫能够无缝集成到现有的向量数据库系统中。

## 🎯 核心要求

### 必需字段 (Required Fields)

新闻爬虫必须提供以下字段，这些是向量化处理的核心数据：

```python
{
    'id': int,                    # 唯一标识符
    'title': str,                 # 新闻标题 (必需，用于向量化)
    'url': str,                   # 新闻链接 (必需，用于去重)
    'media': str,                 # 媒体来源 (必需，用于筛选)
    'create_date': str,           # 创建日期 'YYYY-MM-DD'
    'create_time': str,           # 创建时间 'HH:MM:SS'
    'formatted_time': str,        # 格式化时间 'MM-DD HH:MM'
    'category': str,              # 新闻分类 (必需，用于筛选)
    'ranking_type': str,          # 排行类型
    'rank': int,                  # 排名
    'comment_url': str,           # 评论链接 (可为空)
    'top_num': str,               # 热度数字 (可为空)
    'crawl_time': str,            # 爬取时间 'YYYY-MM-DD HH:MM:SS'
    'content': str                # 新闻内容 (必需，用于向量化)
}
```

### 关键字段说明

#### 🔑 **核心字段** (向量化必需)
- **title**: 新闻标题，长度建议10-200字符
- **content**: 新闻正文内容，长度至少20字符，建议500-2000字符
- **url**: 完整的新闻链接，用于去重检测

#### 📊 **元数据字段** (搜索筛选必需)
- **media**: 媒体名称，如"网易新闻"、"新浪新闻"
- **category**: 新闻分类，如"科技"、"财经"、"体育"
- **create_date**: 新闻发布日期，格式：'2024-07-07'

#### 🏷️ **可选字段** (增强功能)
- **ranking_type**: 排行榜类型，如"热点新闻"、"实时新闻"
- **comment_url**: 评论页面链接
- **top_num**: 热度/点击数等数值

## 🛠️ 技术实现要求

### 1. 爬虫类结构

```python
class YourNewsCrawler:
    def __init__(self, max_news=50, get_detail=True, get_all=False):
        """
        初始化爬虫
        :param max_news: 最大爬取数量
        :param get_detail: 是否获取详细内容
        :param get_all: 是否获取全部新闻
        """
        pass
    
    def crawl(self):
        """
        主爬取方法
        :return: (news_list, count) 元组
        """
        pass
    
    def save_to_csv(self, news_items, filename):
        """
        保存为CSV文件，使用标准字段名
        """
        pass
```

### 2. 数据质量要求

#### ✅ **内容质量**
- 标题长度：10-200字符
- 正文长度：至少20字符，建议500+字符
- 内容清洁：移除HTML标签、特殊字符
- 编码统一：使用UTF-8编码

#### ✅ **数据完整性**
- 必需字段不能为空
- URL必须是有效链接
- 时间格式必须标准化
- ID必须唯一

#### ✅ **去重机制**
- 基于URL进行去重
- 同一URL只保留最新数据
- 标题相似度检测（可选）

### 3. CSV输出格式

```python
fieldnames = [
    'id', 'title', 'url', 'media', 'create_date', 'create_time',
    'formatted_time', 'category', 'ranking_type', 'rank',
    'comment_url', 'top_num', 'crawl_time', 'content'
]
```

## 📝 实现示例

### 基础爬虫模板

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例新闻爬虫
"""

import requests
from bs4 import BeautifulSoup
import csv
from datetime import datetime

class ExampleNewsCrawler:
    def __init__(self, max_news=50, get_detail=True, get_all=False):
        self.max_news = max_news
        self.get_detail = get_detail
        self.get_all = get_all
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    
    def crawl(self):
        """主爬取方法"""
        news_list = []
        
        # 1. 获取新闻列表
        news_urls = self.get_news_list()
        
        # 2. 获取详细内容
        for i, url in enumerate(news_urls):
            if not self.get_all and i >= self.max_news:
                break
                
            news_item = self.parse_news_detail(url, i+1)
            if news_item:
                news_list.append(news_item)
        
        return news_list, len(news_list)
    
    def parse_news_detail(self, url, rank):
        """解析新闻详情"""
        try:
            response = requests.get(url, headers=self.headers)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取数据
            title = soup.find('h1').get_text().strip()
            content = soup.find('div', class_='content').get_text().strip()
            
            return {
                'id': rank,
                'title': title,
                'url': url,
                'media': '示例新闻网',
                'create_date': datetime.now().strftime('%Y-%m-%d'),
                'create_time': datetime.now().strftime('%H:%M:%S'),
                'formatted_time': datetime.now().strftime('%m-%d %H:%M'),
                'category': '综合',
                'ranking_type': '热点新闻',
                'rank': rank,
                'comment_url': '',
                'top_num': '',
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'content': content[:2000]  # 限制长度
            }
        except Exception as e:
            print(f"解析新闻失败: {e}")
            return None
    
    def save_to_csv(self, news_items, filename='example_news.csv'):
        """保存为CSV文件"""
        fieldnames = [
            'id', 'title', 'url', 'media', 'create_date', 'create_time',
            'formatted_time', 'category', 'ranking_type', 'rank',
            'comment_url', 'top_num', 'crawl_time', 'content'
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(news_items)
```

## 🔄 集成流程

### 1. 开发新爬虫
- 按照上述规范实现爬虫类
- 确保数据格式完全匹配
- 进行本地测试验证

### 2. 集成到统一爬虫
```python
# 在 unified_news_crawler.py 中添加
from your_news_crawler import YourNewsCrawler

class UnifiedNewsCrawler:
    def __init__(self):
        self.results = {
            'netease': [],
            'sina': [],
            'your_source': []  # 添加新源
        }
    
    def crawl_your_source(self):
        """爬取你的新闻源"""
        crawler = YourNewsCrawler(...)
        news_list, count = crawler.crawl()
        self.results['your_source'] = news_list
```

### 3. 测试验证
```bash
# 测试新爬虫
python your_news_crawler.py

# 测试向量化处理
python csv_to_vector_processor.py

# 测试搜索功能
python news_search.py -q "测试查询"
```

## ⚠️ 注意事项

### 法律合规
- 遵守robots.txt协议
- 控制爬取频率，避免对服务器造成压力
- 尊重网站的使用条款

### 技术要求
- 异常处理：所有网络请求都要有异常处理
- 编码处理：确保中文内容正确编码
- 内存管理：大量数据处理时注意内存使用

### 性能优化
- 使用连接池复用HTTP连接
- 合理设置请求间隔
- 支持断点续传和增量更新

## 📞 技术支持

如有疑问或需要帮助，请参考：
- 现有爬虫实现：`netease_news_crawler.py`、`sina_news_crawler.py`
- 统一爬虫：`unified_news_crawler.py`
- 数据处理：`csv_to_vector_processor.py`
- 搜索功能：`news_search.py`
