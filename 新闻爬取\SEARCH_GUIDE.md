# 🔍 新闻向量数据库查询指南

## 快速开始

### 基本查询
```bash
# 简单搜索
python news_search.py -q "人工智能"

# 指定返回数量
python news_search.py -q "新能源汽车" -t 10

# 设置相似度阈值
python news_search.py -q "量子计算" --threshold 0.5
```

### 交互式查询
```bash
# 进入交互模式
python news_search.py -i

# 或者直接运行（默认交互模式）
python news_search.py
```

## 🎯 查询功能

### 1. 单次查询
```bash
python news_search.py -q "搜索关键词" [选项]
```

**选项说明：**
- `-t, --top N`: 返回前N条结果（默认5）
- `--threshold X`: 相似度阈值（默认0.3）
- `-c, --category 分类`: 按分类筛选
- `-m, --media 媒体`: 按媒体筛选
- `--content`: 显示新闻内容摘要

**示例：**
```bash
# 搜索科技类新闻
python news_search.py -q "人工智能" -c "科技" -t 10

# 搜索特定媒体的新闻
python news_search.py -q "经济政策" -m "网易新闻" --content

# 高相似度搜索
python news_search.py -q "量子计算" --threshold 0.7 -t 3
```

### 2. 交互式查询
```bash
python news_search.py -i
```

**交互命令：**
- 直接输入关键词搜索
- `help`: 显示帮助信息
- `stats`: 显示数据库统计
- `quit/exit/q`: 退出程序

**高级查询语法：**
```
人工智能 --top=10 --category=科技
新能源汽车 --threshold=0.6 --media=网易新闻
```

### 3. 批量查询
```bash
python news_search.py -b queries.txt -o results.json
```

**查询文件格式：**
```
人工智能技术
新能源汽车
量子计算研究
```

### 4. 数据库统计
```bash
python news_search.py -s
```

## 📊 查询结果格式

### 控制台输出
```
✅ 找到 3 条相关新闻:
--------------------------------------------------------------------------------

1. 📰 人工智能技术发展迅速
   🎯 相似度: 0.763
   🏷️  分类: 科技 | 📰 媒体: 测试媒体
   📅 时间: 2025-07-07 15:00:00
   🔗 链接: https://example.com/news/1
   📝 内容: 人工智能技术在各个领域都取得了重大突破...
```

### JSON输出（批量查询）
```json
[
  {
    "query": "人工智能技术",
    "timestamp": "2025-07-07T15:30:00",
    "results_count": 2,
    "results": [
      {
        "title": "人工智能技术发展迅速",
        "similarity": 0.763,
        "category": "科技",
        "media": "测试媒体",
        "url": "https://example.com/news/1",
        "publish_time": "2025-07-07 15:00:00"
      }
    ]
  }
]
```

## 🔧 高级功能

### 筛选条件

**按分类筛选：**
```bash
python news_search.py -q "技术创新" -c "科技"
```

**按媒体筛选：**
```bash
python news_search.py -q "经济新闻" -m "网易新闻"
```

**组合筛选：**
```bash
python news_search.py -q "人工智能" -c "科技" -m "网易" -t 5
```

### 相似度控制

**高精度搜索（相似度>0.7）：**
```bash
python news_search.py -q "具体新闻标题" --threshold 0.7
```

**宽泛搜索（相似度>0.3）：**
```bash
python news_search.py -q "抽象概念" --threshold 0.3
```

### 批量处理

**创建查询文件：**
```bash
echo -e "人工智能\n新能源汽车\n量子计算" > my_queries.txt
```

**执行批量查询：**
```bash
python news_search.py -b my_queries.txt -o my_results.json
```

## 📈 性能优化

### 查询优化建议

1. **使用具体关键词**：
   - ✅ 好：`"特斯拉电动汽车销量"`
   - ❌ 差：`"汽车"`

2. **合理设置阈值**：
   - 精确匹配：`threshold >= 0.7`
   - 相关匹配：`threshold >= 0.5`
   - 宽泛搜索：`threshold >= 0.3`

3. **限制结果数量**：
   - 快速查看：`top_k = 3-5`
   - 详细分析：`top_k = 10-20`

### 批量查询优化

1. **合理的查询数量**：每批次建议不超过100个查询
2. **API限流考虑**：系统会自动控制请求频率
3. **结果保存**：大批量查询建议保存为JSON文件

## 🚨 常见问题

### Q: 搜索结果为空
**A: 可能原因和解决方案：**
- 降低相似度阈值：`--threshold 0.2`
- 使用更通用的关键词
- 检查数据库是否包含相关内容：`python news_search.py -s`

### Q: 相似度分数偏低
**A: 这是正常现象：**
- 0.7+：高度相关
- 0.5-0.7：中度相关
- 0.3-0.5：低度相关

### Q: 查询速度慢
**A: 优化建议：**
- 减少返回结果数量
- 使用筛选条件缩小范围
- 检查网络连接（向量化需要API调用）

### Q: 批量查询中断
**A: 恢复方法：**
- 检查查询文件格式
- 确认API配置正确
- 使用较小的批次重试

## 💡 使用技巧

### 1. 探索性搜索
```bash
# 先查看数据库统计
python news_search.py -s

# 使用宽泛关键词探索
python news_search.py -q "科技" --threshold 0.3 -t 10
```

### 2. 精确查找
```bash
# 使用具体标题或内容片段
python news_search.py -q "印军全面排查中国产零部件" --threshold 0.7
```

### 3. 主题分析
```bash
# 创建主题查询文件
echo -e "人工智能\n机器学习\n深度学习\n神经网络" > ai_topics.txt
python news_search.py -b ai_topics.txt -o ai_analysis.json
```

### 4. 媒体对比
```bash
# 同一话题在不同媒体的报道
python news_search.py -q "经济政策" -m "网易新闻" -t 5
python news_search.py -q "经济政策" -m "新浪新闻" -t 5
```

---

🎯 **开始使用：** `python news_search.py -i`  
📚 **更多帮助：** 在交互模式中输入 `help`
