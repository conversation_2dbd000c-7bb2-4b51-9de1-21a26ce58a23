#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新浪新闻热点爬虫
爬取 https://news.sina.com.cn/hotnews/ 的热点新闻数据
"""

import requests
import json
import re
from datetime import datetime
from typing import List, Dict
import pandas as pd
from bs4 import BeautifulSoup
import time


class SinaNewsCrawler:
    """新浪新闻爬虫类"""

    def __init__(self):
        self.base_url = "https://top.news.sina.com.cn/ws/GetTopDataList.php"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://news.sina.com.cn/hotnews/',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'
        }

        # 定义各个排行榜的API配置
        self.ranking_configs = {
            '新闻总排行': {
                '点击量排行': {'top_cat': 'www_www_all_suda_suda', 'js_var': 'all_1_data01'},
                '评论数排行': {'top_cat': 'qbpdpl', 'js_var': 'comment_all_data'},
                '分享数排行': {'top_cat': 'total_sharenews_48h', 'js_var': 'share_data', 'special': True},
                '视频排行': {'top_cat': 'video_news_all_by_vv', 'js_var': 'sp_xw_yypdpx_1_data'},
                '图片排行': {'top_cat': 'total_slide_suda', 'js_var': 'slide_image_1_data'}
            },
            '国内新闻': {
                '点击量排行': {'top_cat': 'news_china_suda', 'js_var': 'news_'},
                '评论数排行': {'top_cat': 'gnxwpl', 'js_var': 'news_'},
                '分享数排行': {'top_cat': 'wbrmzfgnxw', 'js_var': 'wbrmzfgnxw_1_data', 'special': True}
            },
            '国际新闻': {
                '点击量排行': {'top_cat': 'news_world_suda', 'js_var': 'news_'},
                '评论数排行': {'top_cat': 'gjxwpl', 'js_var': 'news_'},
                '分享数排行': {'top_cat': 'wbrmzfgwxw', 'js_var': 'wbrmzfgwxw_1_data', 'special': True}
            },
            '社会新闻': {
                '点击量排行': {'top_cat': 'news_society_suda', 'js_var': 'news_'},
                '评论数排行': {'top_cat': 'shxwpl', 'js_var': 'news_'},
                '分享数排行': {'top_cat': 'wbrmzfshxw', 'js_var': 'wbrmzfshxw_1_data', 'special': True}
            },
            '体育新闻': {
                '点击量排行': {'top_cat': 'sports_suda', 'js_var': 'channel_', 'base_url': 'https://top.sports.sina.com.cn/ws/GetTopDataList.php'},
                '评论数排行': {'top_cat': 'tyxwpl', 'js_var': 'channel_pl_', 'base_url': 'https://top.sports.sina.com.cn/ws/GetTopDataList.php'},
                '分享数排行': {'top_cat': 'wbrmzfty', 'js_var': 'wbrmzfty_1_data', 'special': True, 'base_url': 'https://top.sports.sina.com.cn/ws/GetTopDataList.php'}
            },
            '财经新闻': {
                '点击量排行': {'top_cat': 'finance_0_suda', 'js_var': 'channel_', 'base_url': 'https://top.finance.sina.com.cn/ws/GetTopDataList.php'},
                '评论数排行': {'top_cat': 'cjxwpl', 'js_var': 'channel_pl_', 'base_url': 'https://top.finance.sina.com.cn/ws/GetTopDataList.php'},
                '分享数排行': {'top_cat': 'wbrmzfcj', 'js_var': 'wbrmzfcj_1_data', 'special': True, 'base_url': 'https://top.finance.sina.com.cn/ws/GetTopDataList.php'}
            },
            '娱乐新闻': {
                '点击量排行': {'top_cat': 'ent_suda', 'js_var': 'channel_', 'base_url': 'https://top.ent.sina.com.cn/ws/GetTopDataList.php'},
                '评论数排行': {'top_cat': 'ylxwpl', 'js_var': 'channel_pl_', 'base_url': 'https://top.ent.sina.com.cn/ws/GetTopDataList.php'},
                '分享数排行': {'top_cat': 'wbrmzfyl', 'js_var': 'wbrmzfyl_1_data', 'special': True, 'base_url': 'https://top.ent.sina.com.cn/ws/GetTopDataList.php'}
            },
            '科技新闻': {
                '点击量排行': {'top_cat': 'tech_news_suda', 'js_var': 'channel_', 'base_url': 'https://top.tech.sina.com.cn/ws/GetTopDataList.php'},
                '评论数排行': {'top_cat': 'kjxwpl', 'js_var': 'channel_pl_', 'base_url': 'https://top.tech.sina.com.cn/ws/GetTopDataList.php'},
                '分享数排行': {'top_cat': 'wbrmzfkj', 'js_var': 'wbrmzfkj_1_data', 'special': True, 'base_url': 'https://top.tech.sina.com.cn/ws/GetTopDataList.php'}
            },
            '军事新闻': {
                '点击量排行': {'top_cat': 'news_mil_suda', 'js_var': 'mil_'},
                '评论数排行': {'top_cat': 'jsxwpl', 'js_var': 'mil_'},
                '分享数排行': {'top_cat': 'wbrmzfjsxw', 'js_var': 'wbrmzfjsxw_1_data', 'special': True}
            }
        }
    
    def get_hot_news(self, date: str = None, limit: int = 10, get_content: bool = False) -> List[Dict]:
        """
        获取热点新闻数据

        Args:
            date: 日期，格式为 YYYYMMDD，默认为今天
            limit: 获取新闻数量，默认10条
            get_content: 是否获取新闻详细内容，默认False

        Returns:
            新闻列表，每个元素包含：序号、标题、链接、媒体、时间、内容(可选)
        """
        if date is None:
            date = datetime.now().strftime('%Y%m%d')
        
        # 构建请求参数
        params = {
            'top_type': 'day',
            'top_cat': 'www_www_all_suda_suda',
            'top_time': date,
            'top_show_num': '100',
            'top_order': 'DESC',
            'js_var': 'all_1_data01'
        }
        
        try:
            # 发送请求
            response = requests.get(self.base_url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            # 解析JavaScript响应
            js_content = response.text
            
            # 提取JSON数据
            json_data = self._extract_json_from_js(js_content)
            if not json_data:
                print("未能提取到有效的JSON数据")
                return []
            
            # 处理新闻数据
            news_list = self._process_news_data(json_data, limit)

            # 如果需要获取详细内容
            if get_content and news_list:
                print(f"开始获取 {len(news_list)} 条新闻的详细内容...")
                news_list = self._get_news_content(news_list)

            return news_list
            
        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return []
        except Exception as e:
            print(f"处理数据时出错: {e}")
            return []
    
    def _extract_json_from_js(self, js_content: str, js_var: str = 'all_1_data01') -> Dict:
        """从JavaScript内容中提取JSON数据"""
        try:
            # 查找指定变量名的模式，如 all_1_data01 = {...} 或 showContent({...})
            patterns = [
                rf'{re.escape(js_var)}\s*=\s*(\{{.*?\}});',  # 变量赋值模式
                rf'showContent\((\{{.*?\}})\)',  # 回调函数模式
                rf'{re.escape(js_var)}\s*=\s*(\{{.*?\}})',   # 无分号模式
            ]

            for pattern in patterns:
                match = re.search(pattern, js_content, re.DOTALL)
                if match:
                    json_str = match.group(1)
                    return json.loads(json_str)

            print(f"未找到变量 {js_var} 的数据模式")
            return {}

        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return {}
    
    def _process_news_data(self, json_data: Dict, limit: int) -> List[Dict]:
        """处理新闻数据，应用过滤规则"""
        if 'data' not in json_data:
            return []

        news_list = []
        sports_count = 0
        ent_count = 0
        finance_count = 0

        # 处理数据，json_data['data'] 是一个列表
        data_list = json_data['data']

        for i, news_item in enumerate(data_list):
            if len(news_list) >= limit:
                break
            
            url = news_item.get('url', '')
            
            # 应用过滤规则（基于原始JavaScript逻辑）
            if self._should_skip_news(url, sports_count, ent_count, finance_count):
                continue
            
            # 更新计数器
            if re.search(r'sports\.sina\.com\.cn|2012\.sina\.com\.cn', url):
                sports_count += 1
            elif re.search(r'ent\.sina\.com\.cn', url):
                ent_count += 1
            elif re.search(r'finance\.sina\.com\.cn', url):
                finance_count += 1
            
            # 格式化时间
            formatted_time = self._format_time(
                news_item.get('create_date', ''),
                news_item.get('create_time', '')
            )
            
            # 构建新闻条目
            news_entry = {
                '序号': len(news_list) + 1,
                '标题': news_item.get('title', ''),
                '链接': url,
                '媒体': news_item.get('media', '').replace('_稿费', '').replace('微天下', ''),
                '时间': formatted_time
            }
            
            news_list.append(news_entry)
        
        return news_list

    def _process_ranking_data(self, json_data: Dict, limit: int, ranking_type: str, category: str) -> List[Dict]:
        """处理排行榜数据，统一格式"""
        if 'data' not in json_data:
            return []

        news_list = []
        data_list = json_data['data']

        for i, news_item in enumerate(data_list):
            if len(news_list) >= limit:
                break

            # 格式化时间
            formatted_time = self._format_time(
                news_item.get('create_date', ''),
                news_item.get('create_time', '')
            )

            # 统一的新闻条目格式
            news_entry = {
                'id': news_item.get('id', ''),
                'title': news_item.get('title', ''),
                'url': news_item.get('url', ''),
                'media': news_item.get('media', '').replace('_稿费', '').replace('微天下', ''),
                'create_date': news_item.get('create_date', ''),
                'create_time': news_item.get('create_time', ''),
                'formatted_time': formatted_time,
                'category': category,
                'ranking_type': ranking_type,
                'rank': len(news_list) + 1,
                'comment_url': news_item.get('comment_url', ''),
                'top_num': news_item.get('top_num', '0'),
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'content': ''  # 预留内容字段
            }

            news_list.append(news_entry)

        return news_list

    def _should_skip_news(self, url: str, sports_count: int, ent_count: int, finance_count: int) -> bool:
        """判断是否应该跳过该新闻"""
        # 体育新闻限制
        if re.search(r'sports\.sina\.com\.cn|2012\.sina\.com\.cn', url) and sports_count >= 3:
            return True
        
        # 娱乐新闻限制
        if re.search(r'ent\.sina\.com\.cn', url) and ent_count >= 1:
            return True
        
        # 财经新闻限制
        if re.search(r'finance\.sina\.com\.cn', url) and finance_count >= 5:
            return True
        
        # 必须是指定域名的新闻
        allowed_domains = [
            r'sports\.sina\.com\.cn',
            r'2012\.sina\.com\.cn',
            r'ent\.sina\.com\.cn',
            r'finance\.sina\.com\.cn',
            r'news\.sina\.com\.cn',
            r'mil\.news\.sina\.com\.cn'
        ]
        
        if not any(re.search(domain, url) for domain in allowed_domains):
            return True
        
        # 排除社会新闻和论坛
        if re.search(r'news\.sina\.com\.cn/s/|/bbs/', url):
            return True
        
        return False
    
    def _format_time(self, create_date: str, create_time: str) -> str:
        """格式化时间显示"""
        try:
            if create_date and create_time:
                date_parts = create_date.split('-')
                time_parts = create_time.split(':')
                
                if len(date_parts) >= 3 and len(time_parts) >= 2:
                    month = date_parts[1]
                    day = date_parts[2]
                    hour = time_parts[0]
                    minute = time_parts[1]
                    
                    return f"{month}-{day} {hour}:{minute}"
            
            return ""
        except Exception:
            return ""

    def _get_news_content(self, news_list: List[Dict]) -> List[Dict]:
        """获取新闻详细内容"""
        for i, news in enumerate(news_list):
            try:
                print(f"正在获取第 {i+1}/{len(news_list)} 条新闻内容: {news['title'][:30]}...")
                content = self._fetch_article_content(news['url'])
                news['content'] = content

                # 添加延时避免请求过快
                time.sleep(1)

            except Exception as e:
                print(f"获取新闻内容失败: {e}")
                news['content'] = "获取失败"

        return news_list

    def _fetch_article_content(self, url: str) -> str:
        """获取单篇文章的详细内容"""
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'

            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找文章内容容器
            article_div = soup.find('div', {'class': 'article', 'id': 'article'})

            if not article_div:
                # 尝试其他可能的选择器
                article_div = soup.find('div', class_='article')
                if not article_div:
                    article_div = soup.find('div', id='article')

            if not article_div:
                return "未找到文章内容"

            # 提取所有段落内容
            paragraphs = []

            # 获取所有p标签
            p_tags = article_div.find_all('p')

            for p in p_tags:
                # 跳过责任编辑等无关内容
                if 'show_author' in p.get('class', []):
                    continue

                text = p.get_text(strip=True)
                if text and len(text) > 5:  # 过滤掉太短的段落
                    # 清理文本
                    text = re.sub(r'\s+', ' ', text)  # 合并多个空白字符
                    text = text.replace('　　', '').strip()  # 移除全角空格
                    if text:
                        paragraphs.append(text)

            # 如果没有找到p标签，尝试获取div内的所有文本
            if not paragraphs:
                text = article_div.get_text(strip=True)
                if text:
                    # 简单清理
                    text = re.sub(r'\s+', ' ', text)
                    paragraphs.append(text[:1000])  # 限制长度

            return '\n\n'.join(paragraphs) if paragraphs else "内容为空"

        except Exception as e:
            return f"获取内容失败: {str(e)}"

    def get_all_rankings(self, date: str = None, limit: int = 10, get_content: bool = False) -> Dict:
        """
        获取所有排行榜数据

        Args:
            date: 日期，格式为 YYYYMMDD，默认为今天
            limit: 每个排行榜获取新闻数量，默认10条
            get_content: 是否获取新闻详细内容，默认False

        Returns:
            包含所有排行榜数据的字典
        """
        if date is None:
            date = datetime.now().strftime('%Y%m%d')

        all_rankings = {}

        for category, rankings in self.ranking_configs.items():
            print(f"\n正在获取 {category} 数据...")
            all_rankings[category] = {}

            for ranking_type, config in rankings.items():
                print(f"  - {ranking_type}")
                try:
                    news_list = self._get_ranking_data(config, date, limit, ranking_type, category)

                    # 如果需要获取详细内容
                    if get_content and news_list:
                        print(f"    获取 {len(news_list)} 条新闻的详细内容...")
                        news_list = self._get_news_content(news_list)

                    all_rankings[category][ranking_type] = news_list

                    # 添加延时避免请求过快
                    time.sleep(0.5)

                except Exception as e:
                    print(f"    获取失败: {e}")
                    all_rankings[category][ranking_type] = []

        return all_rankings

    def _get_ranking_data(self, config: Dict, date: str, limit: int, ranking_type: str, category: str) -> List[Dict]:
        """获取单个排行榜数据"""
        try:
            # 获取API URL
            api_url = config.get('base_url', self.base_url)

            # 构建请求参数
            params = {
                'top_type': 'day',
                'top_cat': config['top_cat'],
                'top_time': date,
                'top_show_num': '100',
                'top_order': 'DESC',
                'js_var': config['js_var']
            }

            # 特殊处理分享数排行（使用不同的API格式）
            if config.get('special'):
                if 'total_sharenews_48h' in config['top_cat']:
                    params.update({
                        'app_key': '2953667869',
                        'top_time': 'today',
                        'top_channel': 'news',
                        'get_new': '1',
                        'format': 'json',
                        'call_back': 'showContent'
                    })
                else:
                    params['call_back'] = 'showContent'

            # 发送请求
            response = requests.get(api_url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()

            # 解析JavaScript响应
            js_content = response.text

            # 提取JSON数据
            json_data = self._extract_json_from_js(js_content, config['js_var'])
            if not json_data:
                return []

            # 处理新闻数据
            news_list = self._process_ranking_data(json_data, limit, ranking_type, category)

            return news_list

        except Exception as e:
            print(f"    请求失败: {e}")
            return []

    def save_to_csv(self, news_list: List[Dict], filename: str = None):
        """保存新闻数据到CSV文件"""
        if not news_list:
            print("没有数据可保存")
            return
        
        if filename is None:
            filename = f"sina_hot_news_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        df = pd.DataFrame(news_list)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"数据已保存到: {filename}")

    def save_all_rankings_to_csv(self, all_rankings: Dict, base_filename: str = None):
        """保存所有排行榜数据到CSV文件"""
        if base_filename is None:
            base_filename = f"sina_all_rankings_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        for category, rankings in all_rankings.items():
            for ranking_type, news_list in rankings.items():
                if news_list:
                    # 生成文件名
                    safe_category = category.replace('/', '_')
                    safe_ranking = ranking_type.replace('/', '_')
                    filename = f"{base_filename}_{safe_category}_{safe_ranking}.csv"

                    # 保存数据
                    df = pd.DataFrame(news_list)
                    df.to_csv(filename, index=False, encoding='utf-8-sig')
                    print(f"已保存: {filename} ({len(news_list)} 条)")

    def save_unified_data(self, all_rankings: Dict, filename: str = None):
        """保存统一格式的数据到单个文件，方便存入数据库"""
        if filename is None:
            filename = f"sina_news_unified_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

        # 合并所有数据
        all_news = []
        for category, rankings in all_rankings.items():
            for ranking_type, news_list in rankings.items():
                all_news.extend(news_list)

        if all_news:
            # 创建DataFrame并保存
            df = pd.DataFrame(all_news)

            # 确保列的顺序
            column_order = [
                'id', 'title', 'url', 'media', 'create_date', 'create_time',
                'formatted_time', 'category', 'ranking_type', 'rank',
                'comment_url', 'top_num', 'crawl_time', 'content'
            ]

            # 重新排列列
            df = df.reindex(columns=column_order)

            # 保存到CSV
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"\n统一数据已保存到: {filename}")
            print(f"总计 {len(all_news)} 条新闻数据")

            # 打印数据统计
            print(f"\n数据统计:")
            category_stats = df.groupby(['category', 'ranking_type']).size()
            for (category, ranking_type), count in category_stats.items():
                print(f"  {category} - {ranking_type}: {count} 条")

            return filename
        else:
            print("没有数据可保存")
            return None

    def print_news(self, news_list: List[Dict]):
        """打印新闻列表"""
        if not news_list:
            print("没有获取到新闻数据")
            return
        
        print(f"\n{'='*80}")
        print(f"{'新浪新闻热点排行':^76}")
        print(f"{'='*80}")
        print(f"{'序号':<4} {'标题':<50} {'媒体':<15} {'时间':<10}")
        print(f"{'-'*80}")
        
        for news in news_list:
            title = news['标题']
            if len(title) > 45:
                title = title[:45] + "..."
            
            print(f"{news['序号']:<4} {title:<50} {news['媒体']:<15} {news['时间']:<10}")
        
        print(f"{'-'*80}")
        print(f"共获取 {len(news_list)} 条新闻")


def main():
    """主函数"""
    crawler = SinaNewsCrawler()

    print("新浪新闻排行榜爬虫")
    print("=" * 50)

    # 直接获取所有排行榜数据
    print("\n正在获取所有排行榜数据...")

    all_rankings = crawler.get_all_rankings(limit=10, get_content=True)

    if all_rankings:
        # 保存统一格式的数据到单个文件
        unified_filename = crawler.save_unified_data(all_rankings)

        # 显示部分数据示例
        print(f"\n数据示例（新闻总排行-点击量排行前3条）:")
        if '新闻总排行' in all_rankings and '点击量排行' in all_rankings['新闻总排行']:
            sample_news = all_rankings['新闻总排行']['点击量排行'][:3]
            for news in sample_news:
                print(f"{news['rank']}. {news['title']}")
                print(f"   分类: {news['category']} - {news['ranking_type']}")
                print(f"   媒体: {news['media']}")
                print(f"   时间: {news['formatted_time']}")
                if news['content']:
                    content_preview = news['content'][:100]
                    if len(news['content']) > 100:
                        content_preview += "..."
                    print(f"   内容: {content_preview}")
                print()
    else:
        print("未能获取到排行榜数据")


if __name__ == "__main__":
    main()
