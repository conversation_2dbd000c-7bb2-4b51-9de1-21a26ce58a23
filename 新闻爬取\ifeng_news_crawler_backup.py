#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
凤凰网新闻爬虫
支持爬取凤凰网新闻首页的所有新闻标题，包括处理"查看更多"按钮的动态加载
"""

import requests
import json
import time
import csv
import re
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import sys
import os


class IfengNewsCrawler:
    """凤凰网新闻爬虫"""
    
    def __init__(self, max_news=100, get_detail=False, get_all=False):
        """
        初始化爬虫
        :param max_news: 最大新闻数量
        :param get_detail: 是否获取详细内容
        :param get_all: 是否获取所有新闻（忽略max_news限制）
        """
        self.base_url = "https://news.ifeng.com/"
        self.max_news = max_news if not get_all else 9999
        self.get_detail = get_detail
        self.get_all = get_all
        
        # 设置请求头，模拟真实浏览器
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://news.ifeng.com/',
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 存储所有新闻
        self.news_list = []
        
    def get_page_content(self, url):
        """获取页面内容"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return response.text
        except Exception as e:
            print(f"获取页面内容失败: {e}")
            return None
    
    def parse_news_from_html(self, html_content):
        """从HTML内容中解析新闻"""
        soup = BeautifulSoup(html_content, 'html.parser')
        news_items = []
        
        # 查找所有新闻链接
        # 凤凰网的新闻链接通常在 <a> 标签中，href包含 /c/ 路径
        news_links = soup.find_all('a', href=re.compile(r'/c/[a-zA-Z0-9]+'))
        
        for link in news_links:
            try:
                title = link.get_text(strip=True)
                url = link.get('href')
                
                # 跳过空标题或太短的标题
                if not title or len(title) < 5:
                    continue
                
                # 构建完整URL
                if url.startswith('//'):
                    url = 'https:' + url
                elif url.startswith('/'):
                    url = urljoin(self.base_url, url)
                
                # 查找时间信息（通常在附近的元素中）
                time_text = ""
                parent = link.parent
                if parent:
                    # 查找包含时间的文本
                    time_elements = parent.find_all(text=re.compile(r'今天|昨天|\d{2}:\d{2}|\d{4}-\d{2}-\d{2}'))
                    if time_elements:
                        time_text = time_elements[0].strip()
                
                # 查找来源信息
                source = "凤凰网"
                source_elements = parent.find_all(text=True) if parent else []
                for text in source_elements:
                    if any(keyword in text for keyword in ['凤凰', '来源', '作者']):
                        source = text.strip()
                        break
                
                news_item = {
                    'title': title,
                    'url': url,
                    'time': time_text,
                    'source': source,
                    'content': ''
                }
                
                news_items.append(news_item)
                
            except Exception as e:
                print(f"解析新闻项失败: {e}")
                continue
        
        return news_items
    
    def get_real_api_url(self, page=1, last_news_id=None):
        """
        获取真实的API接口URL
        基于真实的凤凰网API接口
        """
        import time

        # 当前时间戳（毫秒）
        timestamp = int(time.time() * 1000)

        # 基础参数
        if page == 1:
            # 第一页使用固定的ID
            base_id = "7347719280965722236"
        else:
            # 后续页面尝试使用上一页最后一条新闻的ID
            base_id = last_news_id if last_news_id else "7347719280965722236"

        timestamp_param = "1751836110000"  # 时间戳参数
        page_size = 20
        category = "3-35191-"  # 新闻分类
        callback_name = "getColumnInfoCallback"

        # 为不同页面生成不同的时间戳偏移
        timestamp_offset = page * 1000 + (page - 1) * 500

        # 构建API URL
        api_url = (
            f"https://shankapi.ifeng.com/api/_/getColumnInfo/_/dynamicFragment/"
            f"{base_id}/{timestamp_param}/{page_size}/{category}/"
            f"{callback_name}?callback={callback_name}&_={timestamp + timestamp_offset}"
        )

        return api_url

    def get_more_news_api(self, page=1, last_news_id=None):
        """
        获取更多新闻的API接口
        使用真实的凤凰网API接口
        """
        try:
            api_url = self.get_real_api_url(page, last_news_id)
            print(f"使用真实API: {api_url[:80]}...")

            # 设置正确的请求头
            headers = self.headers.copy()
            headers.update({
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://news.ifeng.com/',
                'Sec-Fetch-Dest': 'script',
                'Sec-Fetch-Mode': 'no-cors',
                'Sec-Fetch-Site': 'same-site',
            })

            response = self.session.get(api_url, headers=headers, timeout=10)
            print(f"API响应状态: {response.status_code}")

            if response.status_code == 200:
                # 处理JSONP响应
                content = response.text

                if content.startswith('getColumnInfoCallback(') and content.endswith(')'):
                    json_str = content[len('getColumnInfoCallback('):-1]
                    try:
                        data = json.loads(json_str)

                        if data.get('code') == 0:
                            parsed_news = self.parse_real_api_response(data)
                            if parsed_news:
                                print(f"成功解析到 {len(parsed_news)} 条新闻")

                                # 检查是否已经结束
                                is_end = data.get('data', {}).get('isEnd', False)
                                if is_end:
                                    print("API返回isEnd=true，已获取所有数据")

                                return parsed_news
                            else:
                                print("解析新闻数据为空")
                        else:
                            print(f"API返回错误: {data.get('message', '未知错误')}")

                    except json.JSONDecodeError as e:
                        print(f"JSON解析失败: {e}")
                else:
                    print("返回格式不是预期的JSONP格式")

        except Exception as e:
            print(f"真实API请求失败: {e}")

        return []
    
    def parse_real_api_response(self, data):
        """解析真实API响应数据"""
        news_items = []

        try:
            if not isinstance(data, dict) or data.get('code') != 0:
                print("API返回错误或格式不正确")
                return news_items

            # 获取新闻流数据
            newsstream = data.get('data', {}).get('newsstream', [])
            print(f"API返回 {len(newsstream)} 条新闻")

            for item in newsstream:
                if not isinstance(item, dict):
                    continue

                # 提取标题
                title = item.get('title', '').strip()

                # 提取URL
                url = item.get('url', '').strip()

                # 提取时间
                time_str = item.get('newsTime', '').strip()

                # 提取来源
                source = item.get('source', '').strip()
                if not source:
                    source = "凤凰网"

                # 提取其他信息
                news_id = item.get('id', '')
                comment_url = item.get('commentUrl', '')
                summary = item.get('summary', '')

                if title and url:
                    news_item = {
                        'id': news_id,
                        'title': title,
                        'url': url,
                        'time': time_str,
                        'source': source,
                        'content': summary,  # 使用summary作为初始内容
                        'comment_url': comment_url
                    }
                    news_items.append(news_item)

        except Exception as e:
            print(f"解析真实API数据失败: {e}")

        return news_items

    def parse_api_response(self, data):
        """解析API响应数据"""
        # 首先尝试真实API格式
        if isinstance(data, dict) and 'code' in data and 'data' in data:
            return self.parse_real_api_response(data)

        # 兼容其他API格式
        news_items = []

        try:
            # 根据不同的API响应格式解析
            items = []
            if 'data' in data and isinstance(data['data'], list):
                items = data['data']
            elif 'data' in data and 'list' in data['data']:
                items = data['data']['list']
            elif 'list' in data:
                items = data['list']

            for item in items:
                title = item.get('title', item.get('newsTitle', ''))
                url = item.get('url', item.get('newsUrl', item.get('link', '')))
                time_str = item.get('time', item.get('newsTime', item.get('publishTime', '')))
                source = item.get('source', item.get('media', '凤凰网'))

                if title and url:
                    # 确保URL是完整的
                    if url.startswith('//'):
                        url = 'https:' + url
                    elif url.startswith('/'):
                        url = urljoin(self.base_url, url)

                    news_item = {
                        'title': title,
                        'url': url,
                        'time': time_str,
                        'source': source,
                        'content': ''
                    }
                    news_items.append(news_item)

        except Exception as e:
            print(f"解析API响应失败: {e}")

        return news_items
    
    def get_article_content(self, url):
        """获取文章详细内容"""
        if not self.get_detail:
            return ""

        try:
            html = self.get_page_content(url)
            if not html:
                return ""

            soup = BeautifulSoup(html, 'html.parser')

            # 移除无关元素
            self._remove_unwanted_elements(soup)

            # 凤凰网文章内容的精确选择器
            content = self._extract_main_content(soup)

            if content:
                # 清理内容
                content = self._clean_content(content)
                return content[:3000] if content else ""  # 增加长度限制

            return ""

        except Exception as e:
            print(f"获取文章内容失败 {url}: {e}")
            return ""

    def _remove_unwanted_elements(self, soup):
        """移除页面中的无关元素"""
        # 移除导航、菜单、广告等无关元素
        unwanted_selectors = [
            'nav', 'header', 'footer',
            '.nav', '.navigation', '.menu',
            '.ad', '.advertisement', '.banner',
            '.sidebar', '.related', '.recommend',
            '.comment', '.share', '.social',
            '.breadcrumb', '.crumb',
            'script', 'style', 'noscript',
            '.popup', '.modal', '.overlay',
            '.browser-upgrade', '.upgrade-browser'
        ]

        for selector in unwanted_selectors:
            for element in soup.select(selector):
                element.decompose()

    def _extract_main_content(self, soup):
        """提取主要文章内容"""
        # 方法1: 凤凰网特定的内容区域 - 精确匹配
        # 查找包含 "index_text" 的 class
        content_div = soup.find('div', class_=re.compile(r'index_text_'))
        if content_div:
            # 只提取其中的 p 标签内容
            paragraphs = content_div.find_all('p')
            if paragraphs:
                content_parts = []
                for p in paragraphs:
                    # 跳过只包含图片的段落
                    if p.find('img') and not p.get_text(strip=True):
                        continue
                    text = p.get_text(strip=True)
                    if text:  # 只添加非空段落
                        content_parts.append(text)

                if content_parts:
                    return '\n'.join(content_parts)

        # 方法2: 查找文章正文的特定标签
        article_tag = soup.find('article')
        if article_tag:
            paragraphs = article_tag.find_all('p')
            if paragraphs:
                content_parts = []
                for p in paragraphs:
                    text = p.get_text(strip=True)
                    if text and len(text) > 10:  # 过滤掉太短的段落
                        content_parts.append(text)
                if content_parts:
                    return '\n'.join(content_parts)
            return article_tag.get_text(strip=True)

        # 方法3: 查找其他可能的内容区域
        content_patterns = [
            # 凤凰网可能的其他内容区域
            {'tag': 'div', 'class': re.compile(r'.*text.*', re.I)},
            {'tag': 'div', 'class': re.compile(r'.*content.*', re.I)},
            {'tag': 'div', 'class': re.compile(r'.*article.*', re.I)},
            {'tag': 'div', 'id': re.compile(r'.*content.*', re.I)},
            {'tag': 'div', 'id': re.compile(r'.*article.*', re.I)},
        ]

        for pattern in content_patterns:
            elements = soup.find_all(**pattern)
            for element in elements:
                # 优先查找段落
                paragraphs = element.find_all('p')
                if paragraphs:
                    content_parts = []
                    for p in paragraphs:
                        text = p.get_text(strip=True)
                        if text and len(text) > 10:
                            content_parts.append(text)
                    if len(content_parts) >= 2:  # 至少要有2个段落才认为是文章内容
                        combined_text = '\n'.join(content_parts)
                        if (len(combined_text) > 200 and
                            not self._contains_navigation_keywords(combined_text) and
                            self._looks_like_article_content(combined_text)):
                            return combined_text

        return ""

    def _contains_navigation_keywords(self, text):
        """检查文本是否包含导航关键词"""
        nav_keywords = [
            '首页', '资讯', '视频', '直播', '财经', '娱乐', '体育',
            '汽车', '房产', '科技', '读书', '文化', '历史', '军事',
            '旅游', '佛教', '国学', '数码', '健康', '家居', '公益',
            '教育', '酒业', '美食', '更多', '下载', '浏览器',
            '升级浏览器', '版本过低', '谷歌', 'Chrome', '360安全浏览器'
        ]

        # 如果文本开头包含太多导航关键词，可能是导航区域
        text_start = text[:200]
        keyword_count = sum(1 for keyword in nav_keywords if keyword in text_start)
        return keyword_count > 3

    def _looks_like_article_content(self, text):
        """判断文本是否像文章内容"""
        # 文章内容的特征
        if len(text) < 100:
            return False

        # 检查是否包含文章常见的标点符号和结构
        punctuation_count = text.count('。') + text.count('，') + text.count('；')
        if punctuation_count < 5:  # 文章应该有足够的标点符号
            return False

        # 检查是否包含过多的链接文本特征
        if text.count('点击') > 5 or text.count('查看') > 5:
            return False

        return True

    def _clean_content(self, content):
        """清理文章内容"""
        if not content:
            return ""

        # 移除多余的空白字符
        content = re.sub(r'\s+', ' ', content)

        # 移除图片说明等
        content = re.sub(r'/图源[：:][^。]*', '', content)
        content = re.sub(r'图源[：:][^。]*', '', content)

        # 移除凤凰网特有的无关内容
        unwanted_patterns = [
            # 版权声明
            r'"特别声明：.*?services\."',
            r'特别声明：.*?services\.',
            # 下载客户端提示
            r'下载客户端独家抢先看',
            # 时间和地点信息（在标题后面的）
            r'\d{4}年\d{2}月\d{2}日 \d{2}:\d{2}:\d{2}来自[^。]*',
            r'\d{4}年\d{2}月\d{2}日 \d{2}:\d{2}:\d{2}',
            # 编辑信息
            r'本期资深编辑 [^。]*',
            # 其他无关信息
            r'关闭',
            r'亲爱的凤凰网用户[^。]*',
            r'您当前使用的浏览器版本过低[^。]*',
            r'建议升级浏览器',
            r'第三方浏览器推荐[^。]*',
            r'谷歌\(Chrome\)浏览器 下载',
            r'360安全浏览器 下载'
        ]

        for pattern in unwanted_patterns:
            content = re.sub(pattern, '', content, flags=re.DOTALL)

        # 移除标题重复（通常在内容开头）
        lines = content.split('\n')
        if lines:
            # 如果第一行看起来像标题+媒体名+时间的组合，就移除
            first_line = lines[0].strip()
            if (len(first_line) > 50 and
                ('下载客户端' in first_line or
                 re.search(r'\d{4}年\d{2}月\d{2}日', first_line) or
                 '来自' in first_line)):
                lines = lines[1:]

        # 重新组合内容
        content = '\n'.join(line.strip() for line in lines if line.strip())

        # 移除开头和结尾的媒体名称
        media_names = ['极目新闻', '澎湃新闻', '上游新闻', '瞭望智库', '果壳', '医学界']
        for media in media_names:
            if content.startswith(media):
                content = content[len(media):].strip()

        return content.strip()
    
    def crawl_news(self):
        """爬取新闻"""
        print(f"开始爬取凤凰网新闻...")
        print(f"设置: 最大新闻数 {self.max_news}, 获取详细内容: {'是' if self.get_detail else '否'}")
        
        # 1. 首先获取首页内容
        print("正在获取首页新闻...")
        html_content = self.get_page_content(self.base_url)
        if html_content:
            news_items = self.parse_news_from_html(html_content)
            self.news_list.extend(news_items)
            print(f"从首页获取到 {len(news_items)} 条新闻")
        
        # 2. 尝试通过API获取更多新闻
        page = 1
        last_news_id = None
        consecutive_empty_pages = 0

        while len(self.news_list) < self.max_news and page <= 20:  # 最多尝试20页
            print(f"正在获取第 {page} 页更多新闻...")
            api_news = self.get_more_news_api(page, last_news_id)

            if not api_news:
                consecutive_empty_pages += 1
                print(f"第 {page} 页没有更多新闻")
                if consecutive_empty_pages >= 3:  # 连续3页没有数据就停止
                    print("连续多页没有新数据，停止获取")
                    break
            else:
                consecutive_empty_pages = 0  # 重置计数器

                # 去重
                existing_urls = {item['url'] for item in self.news_list}
                new_items = [item for item in api_news if item['url'] not in existing_urls]

                if not new_items:
                    print(f"第 {page} 页没有新的新闻")
                    # 但不立即停止，可能下一页有新数据
                else:
                    self.news_list.extend(new_items)
                    print(f"第 {page} 页获取到 {len(new_items)} 条新新闻，总计 {len(self.news_list)} 条")

                    # 更新最后一条新闻的ID，用于下一页请求
                    if api_news and 'id' in api_news[-1]:
                        last_news_id = api_news[-1]['id']

            page += 1
            time.sleep(2)  # 避免请求过快
        
        # 3. 去重并限制数量
        unique_news = []
        seen_urls = set()
        for item in self.news_list:
            if item['url'] not in seen_urls and len(unique_news) < self.max_news:
                unique_news.append(item)
                seen_urls.add(item['url'])
        
        self.news_list = unique_news
        
        # 4. 获取详细内容（如果需要）
        if self.get_detail and self.news_list:
            print(f"开始获取 {len(self.news_list)} 条新闻的详细内容...")
            for i, item in enumerate(self.news_list):
                print(f"获取详细内容 {i+1}/{len(self.news_list)}: {item['title'][:30]}...")
                item['content'] = self.get_article_content(item['url'])
                time.sleep(0.5)  # 避免请求过快
        
        print(f"爬取完成！共获取 {len(self.news_list)} 条新闻")
        return self.news_list
    
    def save_to_csv(self, filename=None):
        """保存到CSV文件"""
        if not self.news_list:
            return None

        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"ifeng_news_{timestamp}.csv"

        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                # 检查是否有新的字段（真实API返回的数据）
                has_extended_fields = any('id' in item for item in self.news_list)

                if has_extended_fields:
                    fieldnames = ['序号', '新闻ID', '标题', '链接', '时间', '来源', '内容', '评论链接']
                else:
                    fieldnames = ['序号', '标题', '链接', '时间', '来源', '内容']

                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for i, item in enumerate(self.news_list, 1):
                    if has_extended_fields:
                        writer.writerow({
                            '序号': i,
                            '新闻ID': item.get('id', ''),
                            '标题': item['title'],
                            '链接': item['url'],
                            '时间': item['time'],
                            '来源': item['source'],
                            '内容': item['content'],
                            '评论链接': item.get('comment_url', '')
                        })
                    else:
                        writer.writerow({
                            '序号': i,
                            '标题': item['title'],
                            '链接': item['url'],
                            '时间': item['time'],
                            '来源': item['source'],
                            '内容': item['content']
                        })

            print(f"数据已保存到: {filename}")
            return filename

        except Exception as e:
            print(f"保存CSV文件失败: {e}")
            return None
    
    def crawl(self):
        """执行完整的爬取流程"""
        news_list = self.crawl_news()
        if news_list:
            filename = self.save_to_csv()
            return filename, len(news_list)
        return None, 0


def main():
    """主函数"""
    # 默认参数
    max_news = 50
    get_detail = False
    get_all = False
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1].lower() in ['all', 'a', '全部']:
            get_all = True
            max_news = 999
        else:
            try:
                max_news = int(sys.argv[1])
            except ValueError:
                print("错误：新闻数量必须是数字，或使用 'all' 获取全部新闻")
                return
    
    if len(sys.argv) > 2:
        get_detail = sys.argv[2].lower() in ['true', '1', 'yes', 'y']
    
    # 创建爬虫实例
    crawler = IfengNewsCrawler(
        max_news=max_news,
        get_detail=get_detail,
        get_all=get_all
    )
    
    # 开始爬取
    filename, total_count = crawler.crawl()
    
    if filename and total_count > 0:
        print(f"\n爬取成功！")
        print(f"文件: {filename}")
        print(f"总数: {total_count} 条新闻")
        print(f"\n使用说明：")
        print(f"python ifeng_news_crawler.py [新闻数量|all] [是否获取详细内容]")
        print(f"例如：")
        print(f"  python ifeng_news_crawler.py 50 true    # 50条新闻，包含详细内容")
        print(f"  python ifeng_news_crawler.py 100 false  # 100条新闻，不包含详细内容")
        print(f"  python ifeng_news_crawler.py all true   # 获取全部新闻，包含详细内容")
    else:
        print("爬取失败！")


if __name__ == "__main__":
    main()
