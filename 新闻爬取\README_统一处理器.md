# 统一新闻向量化处理系统

## 概述

这是一个改进的新闻向量化处理系统，解决了原系统的以下问题：
- ✅ **统一数据库**: 所有新闻数据存储在同一个向量数据库中，而不是每天创建新的数据库
- ✅ **重复检测**: 基于URL和内容hash的智能重复检测
- ✅ **自动文件管理**: 处理完成后自动归档或删除CSV文件
- ✅ **持久化存储**: 数据累积存储，支持长期数据积累
- ✅ **智能质量评估**: 内容质量评分和过滤
- ✅ **多线程处理**: 提高处理效率

## 系统架构

```
爬取新闻 → 发现CSV → 重复检测 → 向量化 → 统一数据库 → 清理CSV → 统计报告
```

## 核心文件

### 1. `config.py` - 配置管理
- **EmbeddingConfig**: API配置（从.env加载）
- **SystemConfig**: 系统配置（路径、参数等）

### 2. `vector_database.py` - 向量数据库（已增强）
- 添加了重复检测功能
- 支持URL和内容hash索引
- 增强的统计信息
- 线程安全操作

### 3. `unified_news_processor.py` - 统一处理器（新）
- 替代原来的`csv_to_vector_processor.py`
- 支持自动文件发现
- 智能重复检测
- 自动文件管理

### 4. `auto_news_processor.py` - 自动化脚本（新）
- 支持多种运行模式
- 定时处理、监控模式
- 状态查看

## 使用方法

### 1. 环境准备

确保`.env`文件包含必要的API配置：
```env
EMBEDDING_API_KEY=your_api_key
EMBEDDING_PROJECT_ID=your_project_id
EMBEDDING_EASYLLM_ID=your_easyllm_id
```

### 2. 单次处理

处理当前目录下的所有CSV文件：
```bash
python unified_news_processor.py
```

### 3. 自动化处理

#### 单次运行
```bash
python auto_news_processor.py --mode once
```

#### 定时处理（每30分钟）
```bash
python auto_news_processor.py --mode scheduled --interval 30
```

#### 监控模式（检测到新文件自动处理）
```bash
python auto_news_processor.py --mode monitor --check-interval 300
```

#### 查看状态
```bash
python auto_news_processor.py --mode status
```

## 配置说明

### SystemConfig 主要参数

```python
# 路径配置
VECTOR_DB_PATH = 'data/unified_news_vector_db'  # 统一向量数据库路径
CSV_DIR = '.'  # CSV文件目录
ARCHIVE_DIR = 'data/archive'  # 归档目录

# 处理配置
BATCH_SIZE = 10  # 批处理大小
MAX_WORKERS = 3  # 最大线程数
MIN_CONTENT_LENGTH = 20  # 最小内容长度
MIN_QUALITY_SCORE = 0.3  # 最小质量分数

# 重复检测
DUPLICATE_CHECK_ENABLED = True
DUPLICATE_URL_CHECK = True
DUPLICATE_CONTENT_CHECK = True

# 文件管理
AUTO_CLEANUP_CSV = True  # 自动清理CSV
ARCHIVE_PROCESSED_FILES = True  # 归档已处理文件
MAX_CSV_AGE_DAYS = 7  # CSV文件最大保留天数
```

## 处理流程

### 1. 文件发现
- 自动扫描`*news*.csv`文件
- 过滤过旧文件（超过7天）
- 按修改时间排序

### 2. 数据处理
- 内容清洗和质量评估
- 批量向量化（API调用）
- 重复检测（URL + 内容hash）
- 存储到统一向量数据库

### 3. 文件管理
- 成功处理后归档或删除CSV文件
- 保留处理日志
- 生成统计报告

## 重复检测机制

### URL重复检测
- 维护URL索引集合
- 相同URL的新闻被跳过

### 内容重复检测
- 生成内容MD5 hash
- 相似内容被跳过

### 统计信息
- 跟踪重复URL数量
- 跟踪重复内容数量
- 显示在处理报告中

## 质量评估

### 评分标准（0.0-1.0）
- **标题质量** (0-0.3): 长度、特殊字符
- **内容长度** (0-0.4): 字符数量分级
- **内容结构** (0-0.3): 句子数量、新闻关键词

### 过滤条件
- 最小内容长度: 20字符
- 最小质量分数: 0.3

## 监控和日志

### 日志文件
- `data/logs/unified_processor_YYYYMMDD.log`
- `data/logs/auto_processor_YYYYMMDD.log`

### 统计信息
- 处理文件数量
- 成功/失败向量化数量
- 重复检测统计
- 向量数据库状态

## 数据库结构

### 向量数据库
- **向量存储**: NumPy数组（768维）
- **元数据**: SQLite数据库
- **索引**: 分类、来源、时间索引
- **重复检测**: URL和内容hash索引

### 元数据字段
```python
{
    'news_id': '新闻ID',
    'title': '标题',
    'content': '清洗后内容',
    'media': '媒体来源',
    'category': '分类',
    'url': 'URL',
    'publish_time': '发布时间',
    'quality_score': '质量分数',
    'processed_time': '处理时间',
    'source': 'csv_import'
}
```

## 搜索功能

### 相似度搜索
```python
# 在unified_news_processor.py中
processor.test_similarity_search("经济发展", top_k=5)
```

### 搜索参数
- **相似度阈值**: 0.3（可配置）
- **最大结果数**: 10（可配置）
- **搜索算法**: 余弦相似度

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查`.env`文件配置
   - 确认API密钥有效
   - 检查网络连接

2. **文件权限错误**
   - 确保有写入权限
   - 检查目录是否存在

3. **内存不足**
   - 减少`BATCH_SIZE`
   - 减少`MAX_WORKERS`

4. **重复检测过于严格**
   - 调整`CONTENT_SIMILARITY_THRESHOLD`
   - 禁用某种重复检测

### 调试模式
设置环境变量启用详细日志：
```bash
export LOG_LEVEL=DEBUG
python unified_news_processor.py
```

## 性能优化

### 建议配置
- **小数据量** (< 1000条): BATCH_SIZE=5, MAX_WORKERS=2
- **中等数据量** (1000-5000条): BATCH_SIZE=10, MAX_WORKERS=3
- **大数据量** (> 5000条): BATCH_SIZE=15, MAX_WORKERS=4

### API限流
- 默认限制: 60次/分钟
- 自动等待和重试
- 线程安全的请求管理

## 扩展功能

### 自定义质量评估
可以在`evaluate_content_quality`方法中添加自定义评分逻辑。

### 自定义重复检测
可以在`check_duplicate`方法中添加更复杂的重复检测逻辑。

### 数据导出
向量数据库支持导出为多种格式，便于后续分析。
